{"name": "symfony/skeleton", "type": "project", "license": "MIT", "description": "A minimal Symfony project recommended to create bare bones applications", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "api-platform/api-pack": "*", "nelmio/cors-bundle": "*", "phpoffice/phpword": "*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.2.*", "symfony/orm-pack": "*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/webapp-pack": "*", "symfony/yaml": "7.2.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}}
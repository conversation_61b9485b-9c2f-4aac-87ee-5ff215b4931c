<?php return array(
    'root' => array(
        'name' => 'symfony/skeleton',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'api-platform/api-pack' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '22df3fde950a7db5fde5e87d30a82097819c3133',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../api-platform/api-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/doctrine-common' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => 'c1a131816aadc6298e068e94d0305070808c16cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/doctrine-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/doctrine-orm' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '2f64d82c068e545539649995538593a70656d7c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/doctrine-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/documentation' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => 'a41c0d6230c0ed60836c6bba60adb4d08b01e6e2',
            'type' => 'project',
            'install_path' => __DIR__ . '/../api-platform/documentation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/http-cache' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '053f7373c5bddec65a28135eba7e252659aaf8a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/http-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/hydra' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '1541defd83108f9bce6e4d8568dd59d1df6d3923',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/hydra',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/json-schema' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '82b21c0b7a85b0c2c10fdf6f1a88957266a39f8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/json-schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/jsonld' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '4b13c13b6193492e736b13d249b5145512bedc1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/jsonld',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/metadata' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '76a7fd811d147cf60c09d17a046cfd2d85e09cd0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/metadata',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/openapi' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '278d5c70923a2648f112d0c47d913d914d9c1b96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/openapi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/serializer' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '397ee8bc7233601aaa0d34d29fc7e7a6664546f1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/state' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '851ac107fd9ab5a0e4c069d4f1bc39cb79b22def',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/state',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/symfony' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '29e936ccf294bec83880a87ee76237047800c791',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../api-platform/symfony',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'api-platform/validator' => array(
            'pretty_version' => 'v4.1.8',
            'version' => '4.1.8.0',
            'reference' => '80f95b0d5e18d5d483dccf3a796cba6c8edef541',
            'type' => 'library',
            'install_path' => __DIR__ . '/../api-platform/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1ca8f21980e770095a31456042471a57bc4c68fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/collections' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'reference' => '2eb07e5953eed811ce1b309a7478a3b236f2273d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/common' => array(
            'pretty_version' => '3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'd9ea4a54ca2586db781f0265d36bea731ac66ec5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '3.9.4',
            'version' => '3.9.4.0',
            'reference' => 'ec16c82f20be1a7224e65ac67144a29199f87959',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/doctrine-bundle' => array(
            'pretty_version' => '2.14.0',
            'version' => '2.14.0.0',
            'reference' => 'ca6a7350b421baf7fbdefbf9f4993292ed18effb',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../doctrine/doctrine-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/doctrine-migrations-bundle' => array(
            'pretty_version' => '3.4.2',
            'version' => '3.4.2.0',
            'reference' => '5a6ac7120c2924c4c070a869d08b11ccf9e277b9',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../doctrine/doctrine-migrations-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b680156fa328f1dfd874fd48c7026c41570b9c6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'c6222283fa3f4ac679f8b9ced9a4e23f163e80d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/migrations' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '325b61e41d032f5f7d7e2d11cbefff656eadc9ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/migrations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/orm' => array(
            'pretty_version' => '3.3.3',
            'version' => '3.3.3.0',
            'reference' => '1f1891d3e20ef9881e81c2f32c53e9dc88dfc9a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/persistence' => array(
            'pretty_version' => '3.4.0',
            'version' => '3.4.0.0',
            'reference' => '0ea965320cec355dba75031c1b23d4c78362e3ff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/persistence',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/sql-formatter' => array(
            'pretty_version' => '1.5.2',
            'version' => '1.5.2.0',
            'reference' => 'd6d00aba6fd2957fe5216fe2b7673e9985db20c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/sql-formatter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nelmio/cors-bundle' => array(
            'pretty_version' => '2.5.0',
            'version' => '2.5.0.0',
            'reference' => '3a526fe025cd20e04a6a11370cf5ab28dbb5a544',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../nelmio/cors-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.4.0',
            'version' => '5.4.0.0',
            'reference' => '447a020a1f875a434d62f2a401f53b82a396e494',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.6.2',
            'version' => '5.6.2.0',
            'reference' => '92dde6a5919e34835c506ac8c523ef095a95ed62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '679e3ce485b99e84c775d28e2e96fade9a7fb50a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/math' => array(
            'pretty_version' => '0.2.0',
            'version' => '0.2.0.0',
            'reference' => 'fc2eb6d1a61b058d5dac77197059db30ee3c8329',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpword' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => '8392134ce4b5dba65130ba956231a1602b848b7f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpword',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '9b30d6fd026b2c132b3985ce6b23bec09ab3aa68',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '9.2.32',
            'version' => '9.2.32.0',
            'reference' => '85402a822d1ecf1db1096959413d35e1c37cf1a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'reference' => 'cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '5a10147d0aaf65b58940a0b72f71c9ac0423cc67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '9.6.23',
            'version' => '9.6.23.0',
            'reference' => '43d2cb18d0675c38bd44982a5d1d88f6d53d8d95',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/link' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '84b159194ecfd7eaa472280213976e96415433f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/link',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/link-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '2b56bea83a09de3ac06bb18b92f068e60cc6f50b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '1.0.8',
            'version' => '1.0.8.0',
            'reference' => '1fc9f64c0927627ef78ba436c9b17d967e68e120',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'fa0f136dd2334583309d32b62544682ee972b51a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '25f207c40d62b8b7aa32f5ab026c53561964053a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '5.1.5',
            'version' => '5.1.5.0',
            'reference' => '830c43a844f1f8d5b7a1f6d6076b784454d8b7ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '78c00df8f170e02473b682df15bfcdacc3d32d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '5.0.7',
            'version' => '5.0.7.0',
            'reference' => 'bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'e1e4a170560925c26d424b6a03aed157e7dcc5c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '5c9eeac41b290a3712d88851518825ad78f45c71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b4f479ebdbf63ac605d183ece17d8d7fe49c15c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '3.0.4',
            'version' => '3.0.4.0',
            'reference' => '05d5692a7993ecccd56a03e40cd7e5b09b1d404e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/type' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/version' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'c6c1022351a901512170118436c764e473f6de8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/asset' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'cb926cd59fefa1f9b4900b3695f0f846797ba5c0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/asset',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/asset-mapper' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => '6428e4b6d8cff9c5fe6f40ddbee4c9f6bfdaa0b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/asset-mapper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/browser-kit' => array(
            'pretty_version' => 'v7.2.4',
            'version' => '7.2.4.0',
            'reference' => '8ce0ee23857d87d5be493abba2d52d1f9e49da61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/browser-kit',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '8b49dde3f5a5e9867595a3a269977f78418d75ee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/config' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'e0b050b83ba999aa77a3736cb6d5b206d65b9d0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '0e2e3f38c192e93e622e41ec37f4ca70cfedf218',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '601a5ce9aaad7bf10797e3663faefce9e26c24e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/debug-bundle' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '2dade0d1415c08b627379b5ec214ec8424cb2e32',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/debug-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/debug-pack' => array(
            'pretty_version' => 'v1.0.10',
            'version' => '1.0.10.0',
            'reference' => '98ef3cb02f9adb54b98d8bc95c84b896513a51d5',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/debug-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '2ca85496cde37f825bd14f7e3548e2793ca90712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/doctrine-bridge' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'd030ea0d45746bf58d7905402bd45e9c35d412dd',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/doctrine-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/doctrine-messenger' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => 'c353e6ee6b41748d8ea6faa2d0b84ac501e3ec0c',
            'type' => 'symfony-messenger-bridge',
            'install_path' => __DIR__ . '/../symfony/doctrine-messenger',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dom-crawler' => array(
            'pretty_version' => 'v7.2.4',
            'version' => '7.2.4.0',
            'reference' => '19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dom-crawler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dotenv' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '28347a897771d0c28e99b75166dd2689099f3045',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => '102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '910c5db85a5356d0fea57680defec4e99eb9c8c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/expression-language' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '26f4884a455e755e630a5fc372df124a3578da2e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/expression-language',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'b8dce482de9d7c9fe2891155035a7248ab5c7fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.2.2',
            'version' => '7.2.2.0',
            'reference' => '87a71856f2f56e4100373e92529eed3171695cfb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/flex' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'reference' => '62d5c38c7af6280d8605b725364680838b475641',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../symfony/flex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/form' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'e4e75b930d7a1ccd47bd3273c859c28e13d89b08',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/form',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/framework-bundle' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => 'c1c6ee8946491b698b067df2258e07918c25da02',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/framework-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v7.2.4',
            'version' => '7.2.4.0',
            'reference' => '78981a2ffef6437ed92d4d7e2a86a82f256c6dc6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.5.2',
            'version' => '3.5.2.0',
            'reference' => 'ee8d807ab20fcb51267fdace50fbe3494c31e645',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '6023ec7607254c87c5e69fb3558255aca440d72b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'f9dec01e6094a063e738f8945ef69c0cfcf792ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/intl' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'f8a603f978b035d3a1dc23977fc8ae57558177ad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/intl',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '998692469d6e698c6eadc7ef37a6530a9eabb356',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/maker-bundle' => array(
            'pretty_version' => 'v1.63.0',
            'version' => '1.63.0.0',
            'reference' => '69478ab39bc303abfbe3293006a78b09a8512425',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/maker-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/messenger' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '7f71d9d08c7708b758477386e7eaaa8ac78063b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/messenger',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '706e65c72d402539a072d0d6ad105fff6c161ef1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/monolog-bridge' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'bbae784f0456c5a87c89d7c1a3fcc9cbee976c1d',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/monolog-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/monolog-bundle' => array(
            'pretty_version' => 'v3.10.0',
            'version' => '3.10.0.0',
            'reference' => '414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/monolog-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/notifier' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '2ef5e49009bd6a35666f10a27eb1b3aff4ae793e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/notifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '7da8fbac9dcfef75ffc212235d76b2754ce0cf50',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/orm-pack' => array(
            'pretty_version' => 'v2.4.1',
            'version' => '2.4.1.0',
            'reference' => '9d8729016b3a8b0db854c028d15c2dcf4d19b1b4',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/orm-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/password-hasher' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'd8bd3d66d074c0acba1214a0d42f5941a8e1e94d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/password-hasher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/phpunit-bridge' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '6106ae85a0e3ed509d339b7f924788c9cc4e7cfb',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/phpunit-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-iconv' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-icu' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '763d2a91fea5681509ca01acbc1c5e450d127811',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-icu',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php73' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php74' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php80' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php81' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php82' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php84' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '000df7860439609837bbe28670b0be15783b7fbf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php84',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => '87b7c93e57df9d8e39a093d32587702380ff045d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/profiler-pack' => array(
            'pretty_version' => 'v1.0.6',
            'version' => '1.0.6.0',
            'reference' => 'bcd6e80af9819454ac18594362e7875fd1d771c7',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/profiler-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/property-access' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'b28732e315d81fbec787f838034de7d6c9b2b902',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/property-access',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/property-info' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => 'f00fd9685ecdbabe82ca25c7b739ce7bba99302c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/property-info',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'ee9a67edc6baa33e5fae662f94f91fd262930996',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/runtime' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '8e8d09bd69b7f6c0260dd3d58f37bd4fbdeab5ad',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../symfony/runtime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/security-bundle' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '721de227035c6e4c322fb7dd4839586d58bc0cf5',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/security-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/security-core' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '340e120d26b3bf5eee5cea0782aebaa2f36b6722',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/security-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/security-csrf' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '2b4b0c46c901729e4e90719eacd980381f53e0a3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/security-csrf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/security-http' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '324425deb859c6a59a2c2414ae60f742976a193b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/security-http',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/serializer' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'be549655b034edc1a16ed23d8164aa04318c5ec1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/serializer-pack' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '2844d81a5fc86b617b82f44a8bfcaaba1d583eee',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/serializer-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/skeleton' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/stimulus-bundle' => array(
            'pretty_version' => 'v2.24.0',
            'version' => '2.24.0.0',
            'reference' => 'e09840304467cda3324cc116c7f4ee23c8ff227c',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/stimulus-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/stopwatch' => array(
            'pretty_version' => 'v7.2.4',
            'version' => '7.2.4.0',
            'reference' => '5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/stopwatch',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'a214fe7d62bd4df2a76447c67c6b26e1d5e74931',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/test-pack' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => '7b708c588cb3e1c8f0281889f273b920cbddff9b',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/test-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/twig-bridge' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => 'b1942d5515b7f0a18e16fd668a04ea952db2b0f2',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/twig-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/twig-bundle' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'cd2be4563afaef5285bb6e0a06c5445e644a5c01',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/twig-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/twig-pack' => array(
            'pretty_version' => 'v1.0.1',
            'version' => '1.0.1.0',
            'reference' => '08a73e833e07921c464336deb7630f93e85ef930',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/twig-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/type-info' => array(
            'pretty_version' => 'v7.2.5',
            'version' => '7.2.5.0',
            'reference' => 'c4824a6b658294c828e609d3d8dbb4e87f6a375d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/type-info',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '2d294d0c48df244c71c105a169d0190bfb080426',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/ux-turbo' => array(
            'pretty_version' => 'v2.24.0',
            'version' => '2.24.0.0',
            'reference' => '22954300bd0b01ca46f17c7890ea15138d9cf67f',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/ux-turbo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'f7c32e309885a97fc9572335e22c2c2d31f328c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '9c46038cd4ed68952166cf7001b54eb539184ccb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '422b8de94c738830a1e071f59ad14d67417d7007',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/web-link' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'f537556a885e14a1d28f6c759d41e57e93d0a532',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/web-link',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/web-profiler-bundle' => array(
            'pretty_version' => 'v7.2.4',
            'version' => '7.2.4.0',
            'reference' => '4ffde1c860a100533b02697d9aaf5f45759ec26a',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../symfony/web-profiler-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/webapp-pack' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'dec06f563177a83baefa4089ec17c61efdded9b0',
            'type' => 'symfony-pack',
            'install_path' => __DIR__ . '/../symfony/webapp-pack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '0feafffb843860624ddfd13478f481f4c3cd8b23',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/extra-bundle' => array(
            'pretty_version' => 'v3.21.0',
            'version' => '3.21.0.0',
            'reference' => '62d1cf47a1aa009cbd07b21045b97d3d5cb79896',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../twig/extra-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.21.1',
            'version' => '3.21.1.0',
            'reference' => '285123877d4dd97dd7c11842ac5fb7e86e60d81d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'willdurand/negotiation' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '68e9ea0553ef6e2ee8db5c1d98829f111e623ec2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../willdurand/negotiation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);

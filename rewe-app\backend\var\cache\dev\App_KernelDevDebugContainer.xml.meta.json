{"resources": [{"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\composer\\installed.json"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\src\\Kernel.php"}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Symfony\\Component\\Workflow\\DependencyInjection\\WorkflowGuardListenerPass", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Symfony\\Component\\Scheduler\\DependencyInjection\\AddScheduleMessengerPass", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Symfony\\Component\\Workflow\\DependencyInjection\\WorkflowDebugPass", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\bundles.php"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\api_platform.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\asset_mapper.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\cache.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\csrf.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\debug.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\doctrine.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\doctrine_migrations.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\framework.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\mailer.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\messenger.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\monolog.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\nelmio_cors.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\notifier.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\routing.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\security.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\translation.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\twig.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\validator.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\packages\\web_profiler.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\config\\services.yaml"}, {"@type": "Symfony\\Component\\Config\\Resource\\GlobResource", "prefix": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\src", "pattern": "", "recursive": true, "hash": "99aa06d3014798d86001c324468d497f", "forExclusion": false, "excludedPrefixes": {"C:/Users/<USER>/Desktop/KI Kram/Personalapp/rewe-app/backend/src/Entity": true, "C:/Users/<USER>/Desktop/KI Kram/Personalapp/rewe-app/backend/src/Kernel.php": true}}, {"@type": "Symfony\\Component\\Config\\Resource\\FileResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\composer.json"}, {"@type": "Symfony\\Component\\Config\\Resource\\DirectoryResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\translations", "pattern": null}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/config/serializer"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/config/validator"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/FrameworkBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/TwigBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/SecurityBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/DoctrineBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/DoctrineMigrationsBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/NelmioCorsBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/ApiPlatformBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/DebugBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/WebProfilerBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/StimulusBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/TurboBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/TwigExtraBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/MonologBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": false, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates/bundles/MakerBundle"}, {"@type": "Symfony\\Component\\Config\\Resource\\FileExistenceResource", "exists": true, "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend/templates"}, {"@type": "Symfony\\Component\\Config\\Resource\\DirectoryResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\src\\ApiResource", "pattern": "/\\.(xml|ya?ml|php)$/"}, {"@type": "Symfony\\Component\\Config\\Resource\\DirectoryResource", "resource": "C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\src\\Entity", "pattern": "/\\.(xml|ya?ml|php)$/"}, {"@type": "Symfony\\Component\\Config\\Resource\\ReflectionClassResource", "files": {"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\src\\Kernel.php": null}, "className": "App\\Kernel", "hash": "64ff3d4c23f3c0fb6914945d559b9c62"}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\ES256", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\ES384", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\ES512", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\RS256", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\RS384", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\RS512", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\PS256", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\PS384", "exists": [false, null]}, {"@type": "Symfony\\Component\\Config\\Resource\\ClassExistenceResource", "resource": "Jose\\Component\\Signature\\Algorithm\\PS512", "exists": [false, null]}]}
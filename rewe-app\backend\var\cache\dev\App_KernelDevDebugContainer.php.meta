a:58:{i:0;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:90:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\vendor\composer\installed.json";}i:1;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:74:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\src\Kernel.php";}i:2;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:72:"Symfony\Component\Workflow\DependencyInjection\WorkflowGuardListenerPass";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:3;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:72:"Symfony\Component\Scheduler\DependencyInjection\AddScheduleMessengerPass";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:4;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:64:"Symfony\Component\Workflow\DependencyInjection\WorkflowDebugPass";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:5;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:78:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\bundles.php";}i:6;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:93:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\api_platform.yaml";}i:7;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:93:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\asset_mapper.yaml";}i:8;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:86:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\cache.yaml";}i:9;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:85:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\csrf.yaml";}i:10;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:86:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\debug.yaml";}i:11;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\doctrine.yaml";}i:12;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:100:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\doctrine_migrations.yaml";}i:13;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:90:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\framework.yaml";}i:14;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:87:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\mailer.yaml";}i:15;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:90:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\messenger.yaml";}i:16;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:88:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\monolog.yaml";}i:17;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:92:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\nelmio_cors.yaml";}i:18;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\notifier.yaml";}i:19;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:88:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\routing.yaml";}i:20;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\security.yaml";}i:21;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:92:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\translation.yaml";}i:22;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:85:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\twig.yaml";}i:23;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:90:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\validator.yaml";}i:24;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:93:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\packages\web_profiler.yaml";}i:25;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:80:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\config\services.yaml";}i:26;O:46:"Symfony\Component\Config\Resource\GlobResource":6:{s:54:" Symfony\Component\Config\Resource\GlobResource prefix";s:63:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\src";s:55:" Symfony\Component\Config\Resource\GlobResource pattern";s:0:"";s:57:" Symfony\Component\Config\Resource\GlobResource recursive";b:1;s:52:" Symfony\Component\Config\Resource\GlobResource hash";s:32:"99aa06d3014798d86001c324468d497f";s:60:" Symfony\Component\Config\Resource\GlobResource forExclusion";b:0;s:64:" Symfony\Component\Config\Resource\GlobResource excludedPrefixes";a:2:{s:70:"C:/Users/<USER>/Desktop/KI Kram/Personalapp/rewe-app/backend/src/Entity";b:1;s:74:"C:/Users/<USER>/Desktop/KI Kram/Personalapp/rewe-app/backend/src/Kernel.php";b:1;}}i:27;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:73:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\composer.json";}i:28;O:51:"Symfony\Component\Config\Resource\DirectoryResource":2:{s:61:" Symfony\Component\Config\Resource\DirectoryResource resource";s:72:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\translations";s:60:" Symfony\Component\Config\Resource\DirectoryResource pattern";N;}i:29;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:77:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/config/serializer";}i:30;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:76:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/config/validator";}i:31;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:93:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/FrameworkBundle";}i:32;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:88:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/TwigBundle";}i:33;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:92:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/SecurityBundle";}i:34;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:92:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/DoctrineBundle";}i:35;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:102:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/DoctrineMigrationsBundle";}i:36;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:94:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/NelmioCorsBundle";}i:37;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:95:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/ApiPlatformBundle";}i:38;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/DebugBundle";}i:39;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:95:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/WebProfilerBundle";}i:40;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:92:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/StimulusBundle";}i:41;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/TurboBundle";}i:42;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:93:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/TwigExtraBundle";}i:43;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:91:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/MonologBundle";}i:44;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:0;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:89:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates/bundles/MakerBundle";}i:45;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:69:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend/templates";}i:46;O:51:"Symfony\Component\Config\Resource\DirectoryResource":2:{s:61:" Symfony\Component\Config\Resource\DirectoryResource resource";s:75:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\src\ApiResource";s:60:" Symfony\Component\Config\Resource\DirectoryResource pattern";s:20:"/\.(xml|ya?ml|php)$/";}i:47;O:51:"Symfony\Component\Config\Resource\DirectoryResource":2:{s:61:" Symfony\Component\Config\Resource\DirectoryResource resource";s:70:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\src\Entity";s:60:" Symfony\Component\Config\Resource\DirectoryResource pattern";s:20:"/\.(xml|ya?ml|php)$/";}i:48;O:57:"Symfony\Component\Config\Resource\ReflectionClassResource":3:{s:64:" Symfony\Component\Config\Resource\ReflectionClassResource files";a:1:{s:74:"C:\Users\<USER>\Desktop\KI Kram\Personalapp\rewe-app\backend\src\Kernel.php";N;}s:68:" Symfony\Component\Config\Resource\ReflectionClassResource className";s:10:"App\Kernel";s:63:" Symfony\Component\Config\Resource\ReflectionClassResource hash";s:32:"64ff3d4c23f3c0fb6914945d559b9c62";}i:49;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\ES256";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:50;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\ES384";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:51;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\ES512";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:52;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\RS256";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:53;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\RS384";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:54;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\RS512";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:55;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\PS256";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:56;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\PS384";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}i:57;O:56:"Symfony\Component\Config\Resource\ClassExistenceResource":2:{s:66:" Symfony\Component\Config\Resource\ClassExistenceResource resource";s:40:"Jose\Component\Signature\Algorithm\PS512";s:64:" Symfony\Component\Config\Resource\ClassExistenceResource exists";a:2:{i:0;b:0;i:1;N;}}}
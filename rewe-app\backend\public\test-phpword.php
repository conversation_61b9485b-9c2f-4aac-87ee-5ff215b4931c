<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');

require_once '../vendor/autoload.php';

try {
    $result = [
        'phpword_available' => class_exists('PhpOffice\PhpWord\PhpWord'),
        'temp_dir' => sys_get_temp_dir(),
        'temp_writable' => is_writable(sys_get_temp_dir()),
        'php_version' => PHP_VERSION,
        'extensions' => [
            'zip' => extension_loaded('zip'),
            'xml' => extension_loaded('xml'),
            'gd' => extension_loaded('gd')
        ]
    ];
    
    if (class_exists('PhpOffice\PhpWord\PhpWord')) {
        // Teste einfache Dokumenterstellung
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        $section = $phpWord->addSection();
        $section->addText('Test');
        
        $tempFile = tempnam(sys_get_temp_dir(), 'test_') . '.docx';
        $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $writer->save($tempFile);
        
        $result['test_file_created'] = file_exists($tempFile);
        $result['test_file_size'] = file_exists($tempFile) ? filesize($tempFile) : 0;
        
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }
    
    echo json_encode($result, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_PRETTY_PRINT);
}
?>

import uuid
import sys
import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# <PERSON>r<PERSON><PERSON>, ob erforderliche Bibliotheken installiert sind
try:
    from docx import Document
    from docx.shared import Pt, Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
except ImportError as e:
    print("Fehler: Erforderliche Bibliotheken fehlen.")
    print("Bitte installieren Sie die Bibliotheken mit:")
    print("pip install python-dateutil python-docx lxml")
    input("Drücken Sie Enter, um das Fenster zu schließen...")
    sys.exit(1)

try:
    import tkinter
except ImportError as e:
    print("Fehler: tkinter ist nicht verfügbar.")
    print("<PERSON><PERSON><PERSON> sicher, dass Python korrekt installiert ist (tkinter ist in der Standard-Installation enthalten).")
    print("Sie können Python neu installieren von: https://www.python.org/downloads/")
    input("<PERSON><PERSON><PERSON> Sie Enter, um das Fenster zu schließen...")
    sys.exit(1)

# Deutsche Monatsnamen
deutsche_monate = {
    1: "Januar", 2: "Februar", 3: "<PERSON>ärz", 4: "April", 5: "Mai", 6: "Juni",
    7: "Juli", 8: "August", 9: "September", 10: "Oktober", 11: "November", 12: "Dezember"
}

def format_date_deutsch(datum):
    """Formatiert ein Datum mit deutschem Monatsnamen."""
    return f"{datum.day}. {deutsche_monate[datum.month]} {datum.year}"

def create_kundigung():
    try:
        # Eingaben aus dem Formular holen
        eintrittsdatum_input = eintrittsdatum_entry.get().strip()
        try:
            eintrittsdatum = datetime.strptime(eintrittsdatum_input, "%d.%m.%Y")
        except ValueError:
            messagebox.showerror("Fehler", "Ungültiges Eintrittsdatum (Format: TT.MM.JJJJ). Verwende aktuelles Datum.")
            eintrittsdatum = datetime.now()

        anrede = anrede_var.get()
        name = name_entry.get().strip()
        adresse = adresse_entry.get().strip()
        ort = ort_var.get()
        kundigungsart = kundigungsart_var.get()
        datum_input = kundigungsdatum_entry.get().strip()
        try:
            ausstellungsdatum = datetime.strptime(datum_input, "%d.%m.%Y")
        except ValueError:
            messagebox.showerror("Fehler", "Ungültiges Kündigungsdatum (Format: TT.MM.JJJJ). Verwende aktuelles Datum.")
            ausstellungsdatum = datetime.now()
        unterzeichner = unterzeichner_entry.get().strip()

        # Validierung
        if not name:
            messagebox.showerror("Fehler", "Name des Mitarbeiters ist erforderlich.")
            return
        if not adresse:
            messagebox.showerror("Fehler", "Adresse ist erforderlich.")
            return
        if not unterzeichner:
            messagebox.showerror("Fehler", "Unterzeichner ist erforderlich.")
            return
        if not ort:
            messagebox.showerror("Fehler", "Ort ist erforderlich.")
            return

        # Prüfen, ob Eintrittsdatum weniger als 3 Monate zurückliegt
        drei_monate = timedelta(days=90)
        heute = datetime.now()
        probezeit_moeglich = (heute - eintrittsdatum) < drei_monate
        if probezeit_moeglich:
            messagebox.showinfo("Hinweis", "Da das Eintrittsdatum weniger als 3 Monate zurückliegt, ist eine Probezeitkündigung möglich.")

        # Kündigungsdatum berechnen
        if kundigungsart == "fristlos":
            kundigungsdatum = ausstellungsdatum
            vorsorgedatum = (ausstellungsdatum + relativedelta(months=1)).replace(day=15)
        elif kundigungsart == "probezeit":
            kundigungsdatum = ausstellungsdatum + timedelta(days=14)
            vorsorgedatum = kundigungsdatum
        else:  # fristgerecht
            vier_wochen_later = ausstellungsdatum + timedelta(days=28)
            folgemonat_15 = (ausstellungsdatum + relativedelta(months=1)).replace(day=15)
            if vier_wochen_later <= folgemonat_15:
                kundigungsdatum = folgemonat_15
            else:
                kundigungsdatum = (vier_wochen_later + relativedelta(months=1)).replace(day=1) - timedelta(days=1)
            vorsorgedatum = kundigungsdatum

        # Übergabeart
        ubergabeart = "per Einschreiben / Einwurf" if kundigungsart == "fristlos" else "persönliche Übergabe / Posteinwurf"

        # Kündigungstext basierend auf Kündigungsart
        if kundigungsart == "fristlos":
            kundigungstext = f"ich kündige das mit Ihnen bestehende Arbeitsverhältnis fristlos mit Zugang dieses Schreibens, rein vorsorglich form- und fristgerecht zum {format_date_deutsch(vorsorgedatum)}, hilfsweise zum nächstzulässigen Termin."
        elif kundigungsart == "probezeit":
            kundigungstext = f"ich kündige das mit Ihnen bestehende Arbeitsverhältnis mit Zugang dieses Schreibens, form- und fristgerecht in der Probezeit zum {format_date_deutsch(kundigungsdatum)}, hilfsweise zum nächstzulässigen Termin."
        else:
            kundigungstext = f"ich kündige das mit Ihnen bestehende Arbeitsverhältnis mit Zugang dieses Schreibens, form- und fristgerecht zum {format_date_deutsch(kundigungsdatum)}, hilfsweise zum nächstzulässigen Termin."

        # Word-Dokument erstellen
        doc = Document()

        # Kopfzeile mit Logo (rechtsbündig)
        section = doc.sections[0]
        header = section.header
        p = header.paragraphs[0]
        p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        run = p.add_run()
        try:
            run.add_picture('logo.png', width=Inches(1.87))  # Weitere 20% Vergrößerung (1.56 * 1.2 = 1.87 Zoll)
        except FileNotFoundError:
            messagebox.showerror("Fehler", "Logo-Datei 'logo.png' nicht gefunden. Bitte speichern Sie das Logo im gleichen Verzeichnis wie das Skript.")
            return
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)

        # 1,5 Leerzeilen vor dem Formular (reduziert von 3 auf 1,5)
        doc.add_paragraph()
        p = doc.add_paragraph()
        p.paragraph_format.space_after = Pt(6)  # Halbe Zeile (1,5 Leerzeilen insgesamt)

        # Briefkopf und Adressinformationen (linksbündig)
        p = doc.add_paragraph("REWE Dugandzic GmbH & Co. oHG")
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(8)
        p = doc.add_paragraph("Sankt-Rochus-Straße 30, 50181 Bedburg")
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)  # Kein zusätzlicher Abstand
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(8)

        # Absatz nach Absender
        p = doc.add_paragraph("")
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)

        # Übergabeart und Empfängeradresse (direkt nach Absatz)
        p = doc.add_paragraph("- persönliche Übergabe / Posteinwurf -")
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        p.runs[0].bold = True
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)
        p = doc.add_paragraph(anrede)
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        p = doc.add_paragraph(name)
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        adresse_parts = adresse.split(", ")
        p = doc.add_paragraph(adresse_parts[0])  # Straße
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(0)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        if len(adresse_parts) > 1:
            p = doc.add_paragraph(adresse_parts[1])  # PLZ und Ort
            p.paragraph_format.space_before = Pt(0)
            p.paragraph_format.space_after = Pt(12)  # Abstand nach Adresse
            p.runs[0].font.name = 'Calibri'
            p.runs[0].font.size = Pt(11)
        else:
            p.paragraph_format.space_after = Pt(12)

        # Ort und Datum (rechtsbündig, deutsches Format)
        p = doc.add_paragraph(f"{ort}, {format_date_deutsch(ausstellungsdatum)}")
        p.runs[0].bold = True
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)

        # Überschrift
        p = doc.add_paragraph("Kündigung Ihres Arbeitsverhältnisses")
        p.runs[0].bold = True
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        p.paragraph_format.space_before = Pt(12)
        p.paragraph_format.space_after = Pt(12)

        # Anrede
        p = doc.add_paragraph(f"Guten Tag {anrede} {name},")
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)

        # Kündigungstext
        p = doc.add_paragraph(kundigungstext)
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)

        # Standardtext mit unterstrichenen Wörtern
        p = doc.add_paragraph()
        run = p.add_run("Ich weise Sie darauf hin, dass Sie ausnahmslos dazu verpflichtet sind, sich ")
        run.font.name = 'Calibri'
        run.font.size = Pt(11)
        run = p.add_run("unverzüglich")
        run.font.name = 'Calibri'
        run.font.size = Pt(11)
        run.underline = True
        run = p.add_run(" nach Kenntnis des Beendigungszeitpunktes ")
        run.font.name = 'Calibri'
        run.font.size = Pt(11)
        run = p.add_run("persönlich")
        run.font.name = 'Calibri'
        run.font.size = Pt(11)
        run.underline = True
        run = p.add_run(" bei der zuständigen Agentur für Arbeit arbeitsuchend zu melden. Andernfalls mindert sich Ihr Anspruch auf Arbeitslosengeld. Sie müssen sich durch persönliche Vorsprache mit der Agentur für Arbeit in Verbindung setzen und sich dort ergänzende Informationen einholen.")
        run.font.name = 'Calibri'
        run.font.size = Pt(11)
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)

        p = doc.add_paragraph(
            "Weiterhin sind Sie verpflichtet, aktiv nach einer Beschäftigung zu suchen."
        )
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)
        p = doc.add_paragraph(
            "Ihre Arbeitspapiere und ein Zeugnis über den Beschäftigungszeitraum erhalten Sie nach Beendigung des Arbeitsverhältnisses für Ihre Unterlagen zugeschickt."
        )
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)

        # Absender
        p = doc.add_paragraph("REWE Dugandzic GmbH & Co. oHG")
        p.paragraph_format.space_before = Pt(12)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)

        # Neuer Absatz vor Unterzeichner
        p = doc.add_paragraph("")
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)

        # Unterzeichner
        p = doc.add_paragraph(unterzeichner)
        p.paragraph_format.space_before = Pt(0)
        p.paragraph_format.space_after = Pt(12)
        p.runs[0].font.name = 'Calibri'
        p.runs[0].font.size = Pt(11)

        # Dokument speichern
        output_file = "Kundigungsschreiben.docx"
        doc.save(output_file)
        messagebox.showinfo("Erfolg", f"Word-Dokument wurde als '{output_file}' im aktuellen Verzeichnis gespeichert.")
        root.destroy()

    except Exception as e:
        messagebox.showerror("Fehler", f"Ein Fehler ist aufgetreten: {str(e)}\nBitte überprüfen Sie Ihre Eingaben oder die Installation der Bibliotheken.\nInstallieren Sie python-dateutil, python-docx und lxml mit: pip install python-dateutil python-docx lxml")

# GUI erstellen
try:
    root = tk.Tk()
    root.title("Kündigungsgenerator")
    root.geometry("450x750")  # Vergrößert auf 450x750

    # Eintrittsdatum
    tk.Label(root, text="Eintrittsdatum (TT.MM.JJJJ):").pack(pady=5)
    eintrittsdatum_entry = tk.Entry(root)
    eintrittsdatum_entry.pack(pady=5)

    # Anrede (Radiobuttons)
    tk.Label(root, text="Anrede:").pack(pady=5)
    anrede_var = tk.StringVar(value="Herr")
    tk.Radiobutton(root, text="Herr", variable=anrede_var, value="Herr").pack()
    tk.Radiobutton(root, text="Frau", variable=anrede_var, value="Frau").pack()

    # Name
    tk.Label(root, text="Name des Mitarbeiters:").pack(pady=5)
    name_entry = tk.Entry(root)
    name_entry.pack(pady=5)

    # Adresse
    tk.Label(root, text="Adresse (z.B. Straße, PLZ Ort):").pack(pady=5)
    adresse_entry = tk.Entry(root)
    adresse_entry.pack(pady=5)

    # Ort (Radiobuttons)
    tk.Label(root, text="Ort:").pack(pady=5)
    ort_var = tk.StringVar(value="Bedburg")
    tk.Radiobutton(root, text="Swisttal", variable=ort_var, value="Swisttal").pack()
    tk.Radiobutton(root, text="Bedburg", variable=ort_var, value="Bedburg").pack()
    tk.Radiobutton(root, text="Grevenbroich", variable=ort_var, value="Grevenbroich").pack()
    tk.Radiobutton(root, text="Köln", variable=ort_var, value="Köln").pack()

    # Kündigungsart (Radiobuttons)
    tk.Label(root, text="Kündigungsart:").pack(pady=5)
    kundigungsart_var = tk.StringVar(value="fristgerecht")
    tk.Radiobutton(root, text="Fristlos", variable=kundigungsart_var, value="fristlos").pack()
    tk.Radiobutton(root, text="Probezeit", variable=kundigungsart_var, value="probezeit").pack()
    tk.Radiobutton(root, text="Fristgerecht", variable=kundigungsart_var, value="fristgerecht").pack()

    # Kündigungsdatum
    tk.Label(root, text="Kündigungsdatum (TT.MM.JJJJ):").pack(pady=5)
    kundigungsdatum_entry = tk.Entry(root)
    kundigungsdatum_entry.pack(pady=5)

    # Unterzeichner
    tk.Label(root, text="Unterzeichner:").pack(pady=5)
    unterzeichner_entry = tk.Entry(root)
    unterzeichner_entry.pack(pady=5)

    # Bestätigen-Button
    tk.Button(root, text="Bestätigen", command=create_kundigung).pack(pady=20)

    # GUI starten
    root.mainloop()

except Exception as e:
    print(f"Fehler beim Starten des GUI: {str(e)}")
    print("Stellen Sie sicher, dass Python korrekt installiert ist und tkinter verfügbar ist.")
    print("Sie können Python neu installieren von: https://www.python.org/downloads/")
    input("Drücken Sie Enter, um das Fenster zu schließen...")
    sys.exit(1)
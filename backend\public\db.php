<?php
// Datenbank-Funktionen für die REWE Personalmanagement-App

// Simulierte Datenbank (in einer echten Anwendung würde hier eine MySQL-Verbindung stehen)
$db = [
    'employees' => [],
    'employee_data' => [],
    'salary_data' => [],
    'import_status' => []
];

// Initialisiere die Datenbank mit leeren Daten
function initDatabase() {
    global $db;

    // Leere Mitarbeiterliste
    $db['employees'] = [];

    // Leere Mitarbeiterdaten
    $db['employee_data'] = [];

    // Leere Gehaltsdaten
    $db['salary_data'] = [];

    // Leerer Import-Status
    $db['import_status'] = [
        '2025' => [
            '01' => ['employee_data' => false, 'salary_data' => false],
            '02' => ['employee_data' => false, 'salary_data' => false],
            '03' => ['employee_data' => false, 'salary_data' => false],
            '04' => ['employee_data' => false, 'salary_data' => false],
            '05' => ['employee_data' => false, 'salary_data' => false],
            '06' => ['employee_data' => false, 'salary_data' => false],
            '07' => ['employee_data' => false, 'salary_data' => false],
            '08' => ['employee_data' => false, 'salary_data' => false],
            '09' => ['employee_data' => false, 'salary_data' => false],
            '10' => ['employee_data' => false, 'salary_data' => false],
            '11' => ['employee_data' => false, 'salary_data' => false],
            '12' => ['employee_data' => false, 'salary_data' => false]
        ]
    ];
}

// Lade die Datenbank aus der JSON-Datei, falls vorhanden
function loadDatabase() {
    global $db;
    $jsonFile = __DIR__ . '/db_data.json';

    // Wenn db_data.json nicht existiert, versuche data.json zu laden (für Kompatibilität)
    if (!file_exists($jsonFile)) {
        $jsonFile = __DIR__ . '/data.json';
    }

    if (file_exists($jsonFile)) {
        $jsonData = file_get_contents($jsonFile);
        $loadedDb = json_decode($jsonData, true);

        if ($loadedDb && is_array($loadedDb)) {
            error_log("Datenbank aus JSON-Datei geladen: " . $jsonFile);

            // Stelle sicher, dass alle erforderlichen Schlüssel vorhanden sind
            $db['employees'] = $loadedDb['employees'] ?? [];
            $db['employee_data'] = $loadedDb['employee_data'] ?? [];
            $db['salary_data'] = $loadedDb['salary_data'] ?? [];
            $db['import_status'] = $loadedDb['import_status'] ?? [];

            error_log("Geladene Daten: " . count($db['employees']) . " Mitarbeiter, " .
                      count($db['employee_data']) . " Mitarbeiterdaten, " .
                      count($db['salary_data']) . " Gehaltsdaten");
            return true;
        }
    }

    // Wenn keine Datei existiert oder die Datei ungültig ist, initialisiere die Datenbank
    error_log("Keine gültige JSON-Datei gefunden, initialisiere Datenbank");
    initDatabase();
    return false;
}

// Lade die Datenbank beim Start oder initialisiere sie neu
loadDatabase();
error_log("Datenbank geladen");

// Funktion zum Abrufen aller Mitarbeiter mit Filtern
function getEmployees($filters = []) {
    global $db;

    $result = $db['employees'];

    // Filtern nach Suchbegriff (in allen Feldern)
    if (isset($filters['search']) && !empty($filters['search'])) {
        $search = strtolower($filters['search']);
        $result = array_filter($result, function($employee) use ($search) {
            foreach ($employee as $key => $value) {
                if (is_string($value) && strpos(strtolower($value), $search) !== false) {
                    return true;
                }
            }
            return false;
        });
    }

    // Filtern nach Abteilung
    if (isset($filters['department']) && !empty($filters['department'])) {
        $result = array_filter($result, function($employee) use ($filters) {
            return $employee['department'] === $filters['department'];
        });
    }

    // Filtern nach Marktnummer
    if (isset($filters['marketNumber']) && !empty($filters['marketNumber'])) {
        $result = array_filter($result, function($employee) use ($filters) {
            return $employee['marketNumber'] === $filters['marketNumber'];
        });
    }

    // Filtern nach Aktivitätskennzeichen
    if (isset($filters['active'])) {
        $active = $filters['active'] === 'true';
        $result = array_filter($result, function($employee) use ($active) {
            return $employee['active'] === $active;
        });
    }

    return array_values($result);
}

// Funktion zum Aktualisieren eines Mitarbeiters
function updateEmployee($id, $data) {
    global $db;

    // Finde den Mitarbeiter
    $employeeIndex = null;
    foreach ($db['employees'] as $index => $emp) {
        if ($emp['id'] == $id) {
            $employeeIndex = $index;
            break;
        }
    }

    if ($employeeIndex === null) {
        return false;
    }

    // Aktualisiere die Daten
    if (isset($data['workArea'])) {
        $db['employees'][$employeeIndex]['workArea'] = $data['workArea'];
        error_log("Einsatzbereich für Mitarbeiter ID $id aktualisiert: " . $data['workArea']);
    }

    // Weitere Felder könnten hier aktualisiert werden

    // Speichere die Änderungen in der Datenbank
    saveDatabase();

    return true;
}

// Funktion zum Speichern der Datenbank
function saveDatabase() {
    global $db;

    // Speichere die Datenbank in der Datei
    $dbFile = __DIR__ . '/db_data.json';
    $result = file_put_contents($dbFile, json_encode($db, JSON_PRETTY_PRINT));

    if ($result === false) {
        error_log("FEHLER: Konnte Datenbank nicht in $dbFile speichern!");
        return false;
    }

    error_log("Datenbank erfolgreich in $dbFile gespeichert (" . count($db['employees']) . " Mitarbeiter, " .
              count($db['employee_data']) . " Mitarbeiterdaten, " .
              count($db['salary_data']) . " Gehaltsdaten)");

    // Erstelle auch eine Sicherungskopie
    $backupFile = __DIR__ . '/db_backup.json';
    $backupResult = copy($dbFile, $backupFile);

    if ($backupResult === false) {
        error_log("WARNUNG: Konnte keine Sicherungskopie in $backupFile erstellen!");
    } else {
        error_log("Sicherungskopie erfolgreich in $backupFile erstellt");
    }

    // Speichere auch in data.json für Kompatibilität
    $dataFile = __DIR__ . '/data.json';
    $dataResult = file_put_contents($dataFile, json_encode($db, JSON_PRETTY_PRINT));

    if ($dataResult === false) {
        error_log("WARNUNG: Konnte Datenbank nicht in $dataFile speichern!");
    } else {
        error_log("Datenbank erfolgreich in $dataFile gespeichert (für Kompatibilität)");
    }

    return true;
}

// Funktion zum Abrufen eines einzelnen Mitarbeiters mit Details
function getEmployeeDetails($id) {
    global $db;

    // Finde den Mitarbeiter
    $employee = null;
    foreach ($db['employees'] as $emp) {
        if ($emp['id'] == $id) {
            $employee = $emp;
            break;
        }
    }

    if (!$employee) {
        return null;
    }

    // Füge Stammdaten und Gehaltsdaten hinzu
    $personnelNumber = $employee['personnelNumber'];
    error_log("Hole Details für Mitarbeiter ID $id, Personalnummer $personnelNumber, Name: " . $employee['name']);

    // Debug: Zeige alle Schlüssel in salary_data
    $keys = array_keys($db['salary_data']);
    error_log("Alle Schlüssel in salary_data: " . implode(", ", array_slice($keys, 0, 10)) . "... (insgesamt " . count($keys) . " Schlüssel)");

    // Sammle alle Gehaltsdaten für diesen Mitarbeiter
    $salaryData = [];
    foreach ($db['salary_data'] as $key => $data) {
        // Prüfe, ob die Daten zu diesem Mitarbeiter gehören
        if ((strpos($key, $personnelNumber . '_') === 0) ||
            (isset($data['PersNr']) && $data['PersNr'] == $personnelNumber)) {

            // Debug: Zeige gefundene Daten
            error_log("Gefundene Gehaltsdaten für Schlüssel $key: " . json_encode(array_slice($data, 0, 5)));

            // Kopiere die Daten, um das Original nicht zu verändern
            $dataCopy = $data;

            // Stelle sicher, dass Monat_Jahr gesetzt ist
            if (!isset($dataCopy['Monat_Jahr']) || empty($dataCopy['Monat_Jahr'])) {
                // Versuche, Monat_Jahr aus dem Schlüssel zu extrahieren
                if (preg_match('/' . $personnelNumber . '_(\d{2})(\d{4})_/', $key, $matches)) {
                    $dataCopy['Monat_Jahr'] = $matches[1] . '.' . $matches[2];
                    error_log("Monat_Jahr aus Schlüssel extrahiert: " . $dataCopy['Monat_Jahr']);
                }
            }

            // Stelle sicher, dass PersNr gesetzt ist
            if (!isset($dataCopy['PersNr']) || empty($dataCopy['PersNr'])) {
                $dataCopy['PersNr'] = $personnelNumber;
            }

            // Stelle sicher, dass Mitarbeiter gesetzt ist
            if (!isset($dataCopy['Mitarbeiter']) || empty($dataCopy['Mitarbeiter'])) {
                $dataCopy['Mitarbeiter'] = $employee['name'];
            }

            // Stelle sicher, dass Stelle gesetzt ist
            if (!isset($dataCopy['Stelle']) || empty($dataCopy['Stelle'])) {
                $dataCopy['Stelle'] = $employee['position'] ?? 'Nicht angegeben';
            }

            // Stelle sicher, dass Abteilung gesetzt ist
            if (!isset($dataCopy['Abteilung']) || empty($dataCopy['Abteilung'])) {
                $dataCopy['Abteilung'] = $employee['department'] ?? 'Nicht angegeben';
            }

            $salaryData[] = $dataCopy;
        }
    }

    // Wenn keine Gehaltsdaten gefunden wurden, versuche es mit dem alten Format
    if (empty($salaryData) && isset($db['salary_data'][$personnelNumber])) {
        $salaryData[] = $db['salary_data'][$personnelNumber];
    }

    error_log("Gefundene Gehaltsdaten für Mitarbeiter $personnelNumber: " . count($salaryData));

    // Wenn immer noch keine Gehaltsdaten gefunden wurden, erstelle einen Dummy-Datensatz
    if (empty($salaryData)) {
        error_log("Keine Gehaltsdaten gefunden, erstelle Dummy-Datensatz für $personnelNumber");
        $dummyData = [
            'PersNr' => $personnelNumber,
            'Mitarbeiter' => $employee['name'],
            'Stelle' => $employee['position'] ?? 'Nicht angegeben',
            'Abteilung' => $employee['department'] ?? 'Nicht angegeben',
            'Monat_Jahr' => '04.2025',
            'Kostenstelle' => $employee['marketNumber'] ?? '0000',
            'Geburtsdatum' => $employee['birthdate'] ?? 'Nicht angegeben',
            'Eintrittsdatum(anrechenbar)' => $employee['entryDate'] ?? 'Nicht angegeben',
            'Austrittsdatum' => $employee['exitDate'] ?? 'Nicht angegeben',
            'Vertragsart' => 'unbefristet',
            'Tarifgebiet Kuerzel' => 'NRW/W',
            'Tarifgebiet' => 'NRW/W Partner',
            'Tarifgruppe' => 'TG G M',
            'Tarifstufe' => 'A',
            'BS-Kennzeichen' => 'V2',
            'Wochen-Stunden' => '38',
            'Soll-Stunden' => '164.67',
            'Stunden bezahlt' => '164.67',
            'Mehrarbeitsstd. bez.' => '0',
            'Brutto Std-Lohn' => '15.0',
            'vertragliche Bezuege' => '2470',
            'Basisbezuege vertraglich' => '2470',
            'Zulagen Gesamt vertraglich' => '0',
            'Funktionszulagen vertraglich' => '0',
            'freiwillige Zulagen vertraglich' => '0',
            'Mehrarbeitspauschale vertraglich' => '0',
            'Zulage Kasse vertraglich' => '0',
            'sonstige Zulagen vertraglich' => '0',
            'bezahlte Fehlzeiten' => '0',
            'AG-SV-Gesamt' => '500.41',
            'Gesamtkosten/ h' => '18.04',
            'Gesamtkosten' => '2970.41'
        ];
        $salaryData[] = $dummyData;
    }

    // Zeige alle gefundenen Monat_Jahr-Werte vor der Sortierung
    if (!empty($salaryData)) {
        $monthYears = [];
        foreach ($salaryData as $data) {
            if (isset($data['Monat_Jahr'])) {
                $monthYears[] = $data['Monat_Jahr'];
            } else {
                $monthYears[] = 'undefined';
            }
        }
        error_log("Monat_Jahr vor Sortierung: " . implode(", ", $monthYears));
    }

    // Sortiere die Gehaltsdaten nach Monat_Jahr (falls vorhanden)
    if (!empty($salaryData)) {
        usort($salaryData, function($a, $b) {
            // Stelle sicher, dass beide Datensätze Monat_Jahr haben
            if (!isset($a['Monat_Jahr']) || empty($a['Monat_Jahr'])) {
                return 1; // Datensätze ohne Monat_Jahr kommen ans Ende
            }
            if (!isset($b['Monat_Jahr']) || empty($b['Monat_Jahr'])) {
                return -1; // Datensätze ohne Monat_Jahr kommen ans Ende
            }

            // Konvertiere Monat_Jahr (MM.YYYY) in ein sortierbares Format
            $monthYearA = explode('.', $a['Monat_Jahr']);
            $monthYearB = explode('.', $b['Monat_Jahr']);

            if (count($monthYearA) == 2 && count($monthYearB) == 2) {
                $yearA = intval($monthYearA[1]);
                $monthA = intval($monthYearA[0]);
                $yearB = intval($monthYearB[1]);
                $monthB = intval($monthYearB[0]);

                // Vergleiche zuerst die Jahre, dann die Monate
                if ($yearA != $yearB) {
                    return $yearB - $yearA; // Neuestes Jahr zuerst
                }
                return $monthB - $monthA; // Neuester Monat zuerst
            }

            // Fallback: Vergleiche die Strings direkt
            return strcmp($b['Monat_Jahr'], $a['Monat_Jahr']); // Neueste zuerst
        });

        // Zeige alle Monat_Jahr-Werte nach der Sortierung
        $monthYears = [];
        foreach ($salaryData as $data) {
            if (isset($data['Monat_Jahr'])) {
                $monthYears[] = $data['Monat_Jahr'];
            } else {
                $monthYears[] = 'undefined';
            }
        }
        error_log("Monat_Jahr nach Sortierung: " . implode(", ", $monthYears));
    }

    // Verwende die neuesten Gehaltsdaten für die Anzeige
    $latestSalaryData = !empty($salaryData) ? $salaryData[0] : null;

    if ($latestSalaryData) {
        error_log("Neueste Gehaltsdaten für $personnelNumber: Monat_Jahr=" .
                 ($latestSalaryData['Monat_Jahr'] ?? 'nicht gesetzt'));

        // Stelle sicher, dass alle wichtigen Felder vorhanden sind
        if (!isset($latestSalaryData['PersNr']) || empty($latestSalaryData['PersNr'])) {
            $latestSalaryData['PersNr'] = $personnelNumber;
        }

        if (!isset($latestSalaryData['Mitarbeiter']) || empty($latestSalaryData['Mitarbeiter'])) {
            $latestSalaryData['Mitarbeiter'] = $employee['name'];
        }
    } else {
        error_log("Keine Gehaltsdaten für $personnelNumber gefunden");
    }

    // Stelle sicher, dass alle Gehaltsdaten vollständig sind
    foreach ($salaryData as &$data) {
        if (!isset($data['PersNr']) || empty($data['PersNr'])) {
            $data['PersNr'] = $personnelNumber;
        }

        if (!isset($data['Mitarbeiter']) || empty($data['Mitarbeiter'])) {
            $data['Mitarbeiter'] = $employee['name'];
        }

        // Stelle sicher, dass Monat_Jahr im richtigen Format ist
        if (isset($data['Monat_Jahr']) && !empty($data['Monat_Jahr'])) {
            $parts = explode('.', $data['Monat_Jahr']);
            if (count($parts) == 2) {
                $month = intval($parts[0]);
                $year = intval($parts[1]);
                $data['Monat_Jahr'] = sprintf("%02d.%04d", $month, $year);
            }
        }
    }

    // Sortiere die Gehaltsdaten erneut, um sicherzustellen, dass sie korrekt sortiert sind
    if (!empty($salaryData)) {
        usort($salaryData, function($a, $b) {
            // Stelle sicher, dass beide Datensätze Monat_Jahr haben
            if (!isset($a['Monat_Jahr']) || empty($a['Monat_Jahr'])) {
                return 1; // Datensätze ohne Monat_Jahr kommen ans Ende
            }
            if (!isset($b['Monat_Jahr']) || empty($b['Monat_Jahr'])) {
                return -1; // Datensätze ohne Monat_Jahr kommen ans Ende
            }

            // Konvertiere Monat_Jahr (MM.YYYY) in ein sortierbares Format
            $monthYearA = explode('.', $a['Monat_Jahr']);
            $monthYearB = explode('.', $b['Monat_Jahr']);

            if (count($monthYearA) == 2 && count($monthYearB) == 2) {
                $yearA = intval($monthYearA[1]);
                $monthA = intval($monthYearA[0]);
                $yearB = intval($monthYearB[1]);
                $monthB = intval($monthYearB[0]);

                // Vergleiche zuerst die Jahre, dann die Monate
                if ($yearA != $yearB) {
                    return $yearB - $yearA; // Neuestes Jahr zuerst
                }
                return $monthB - $monthA; // Neuester Monat zuerst
            }

            // Fallback: Vergleiche die Strings direkt
            return strcmp($b['Monat_Jahr'], $a['Monat_Jahr']); // Neueste zuerst
        });

        // Aktualisiere die neuesten Gehaltsdaten
        $latestSalaryData = $salaryData[0];

        // Zeige alle Monat_Jahr-Werte nach der erneuten Sortierung
        $monthYears = [];
        foreach ($salaryData as $data) {
            if (isset($data['Monat_Jahr'])) {
                $monthYears[] = $data['Monat_Jahr'];
            } else {
                $monthYears[] = 'undefined';
            }
        }
        error_log("Monat_Jahr nach erneuter Sortierung: " . implode(", ", $monthYears));
    }

    $details = [
        'basic' => $employee,
        'employee_data' => isset($db['employee_data'][$personnelNumber]) ? $db['employee_data'][$personnelNumber] : null,
        'salary_data' => $latestSalaryData,
        'all_salary_data' => $salaryData
    ];

    return $details;
}

// Funktion zum Abrufen des Import-Status
function getImportStatus() {
    global $db;
    return $db['import_status'];
}

// Funktion zum Aktualisieren des Import-Status
function updateImportStatus($year, $month, $type, $status, $statusType = null) {
    global $db;

    if (!isset($db['import_status'][$year])) {
        $db['import_status'][$year] = [];
    }

    if (!isset($db['import_status'][$year][$month])) {
        $db['import_status'][$year][$month] = ['employee_data' => false, 'salary_data' => false];
    }

    // Wenn ein spezieller Status-Typ angegeben wurde (z.B. 'processing')
    if ($statusType) {
        if (!isset($db['import_status'][$year][$month][$type . '_status'])) {
            $db['import_status'][$year][$month][$type . '_status'] = '';
        }
        $db['import_status'][$year][$month][$type . '_status'] = $statusType;
    }

    $db['import_status'][$year][$month][$type] = $status;

    // Speichere die Änderungen in der Datenbank
    saveDatabase();

    return $db['import_status'];
}

// Funktion zur Validierung von Datumswerten
function validateDate($date) {
    if (empty($date)) return true; // Leere Daten sind erlaubt

    // Prüfe, ob das Datum im Format TT.MM.JJJJ ist
    if (!preg_match('/^\d{1,2}\.\d{1,2}\.\d{4}$/', $date)) {
        error_log("Ungültiges Datumsformat: $date");
        return false;
    }

    // Zerlege das Datum
    list($day, $month, $year) = explode('.', $date);

    // Prüfe, ob das Datum gültig ist
    if (!checkdate((int)$month, (int)$day, (int)$year)) {
        error_log("Ungültiges Datum: $date");
        return false;
    }

    return true;
}

// Funktion zur Validierung von numerischen Werten
function validateNumeric($value) {
    if (empty($value)) return true; // Leere Daten sind erlaubt

    // Entferne Währungssymbole und Tausendertrennzeichen
    $cleanValue = preg_replace('/[^\d.,\-]/', '', $value);
    $cleanValue = str_replace(',', '.', $cleanValue);

    // Prüfe, ob der Wert numerisch ist
    if (!is_numeric($cleanValue)) {
        error_log("Ungültiger numerischer Wert: $value");
        return false;
    }

    return true;
}

// Funktion zum Importieren von CSV-Daten mit Validierung
function importCSV($type, $data, $year, $month) {
    global $db;

    // Debug-Informationen
    error_log("Starte Import für $type ($year/$month) mit " . count($data) . " Datensätzen");

    // Initialisiere Zähler für Statistiken
    $newEmployees = 0;
    $updatedEmployees = 0;
    $skippedRows = 0;
    $validationErrors = 0;

    // Verarbeite die CSV-Daten
    if ($type === 'employee_data') {
        // Verarbeite Stammdaten
        error_log("Verarbeite Stammdaten");

        // Überprüfe die Spaltennamen und versuche, die richtigen Spalten zu finden
        $firstRow = $data[0] ?? [];
        error_log("Verfügbare Spalten: " . implode(", ", array_keys($firstRow)));

        // Versuche, die Spalte für die Personalnummer zu finden
        $personnelNumberKey = null;
        $possibleKeys = ['personnelNumber', 'PersNr', 'PersNR', 'Personalnummer', 'Personal-Nr'];
        foreach ($possibleKeys as $key) {
            if (isset($firstRow[$key])) {
                $personnelNumberKey = $key;
                error_log("Personalnummer-Spalte gefunden: $key");
                break;
            }
        }

        if (!$personnelNumberKey) {
            error_log("FEHLER: Keine Spalte für Personalnummer gefunden!");
            return false;
        }

        // Versuche, andere wichtige Spalten zu finden
        $nameKey = null;
        $possibleNameKeys = ['name', 'Name', 'Mitarbeiter', 'MitarbeiterName'];
        foreach ($possibleNameKeys as $key) {
            if (isset($firstRow[$key])) {
                $nameKey = $key;
                error_log("Name-Spalte gefunden: $key");
                break;
            }
        }

        $departmentKey = null;
        $possibleDeptKeys = ['department', 'Department', 'Abteilung'];
        foreach ($possibleDeptKeys as $key) {
            if (isset($firstRow[$key])) {
                $departmentKey = $key;
                error_log("Abteilungs-Spalte gefunden: $key");
                break;
            }
        }

        $locationKey = null;
        $possibleLocKeys = ['location', 'Location', 'Standort', 'Ort'];
        foreach ($possibleLocKeys as $key) {
            if (isset($firstRow[$key])) {
                $locationKey = $key;
                error_log("Standort-Spalte gefunden: $key");
                break;
            }
        }

        $storeKey = null;
        $possibleStoreKeys = ['store', 'Store', 'Markt', 'Marktnummer', 'Kostenstelle', 'Markt/ Einheit'];
        foreach ($possibleStoreKeys as $key) {
            if (isset($firstRow[$key])) {
                $storeKey = $key;
                error_log("Markt-Spalte gefunden: $key");
                break;
            }
        }

        foreach ($data as $index => $row) {
            // Debug: Zeige die ersten paar Zeilen
            if ($index < 3) {
                error_log("Zeile $index: " . json_encode($row));
            }

            $personnelNumber = $row[$personnelNumberKey] ?? '';
            if (empty($personnelNumber)) {
                error_log("Überspringe Zeile $index: Keine Personalnummer gefunden");
                $skippedRows++;
                continue;
            }

            // Validiere wichtige Felder
            $validationFailed = false;

            // Validiere Datumswerte
            $dateFields = ['Geburtsdatum', 'Eintrittsdatum', 'Austrittsdatum'];
            foreach ($dateFields as $field) {
                if (isset($row[$field]) && !empty($row[$field]) && !validateDate($row[$field])) {
                    error_log("Validierungsfehler in Zeile $index: Ungültiges Datum in Feld '$field': " . $row[$field]);
                    $validationFailed = true;
                }
            }

            if ($validationFailed) {
                error_log("Überspringe Zeile $index wegen Validierungsfehlern");
                $validationErrors++;
                continue;
            }

            // Speichere Stammdaten
            $db['employee_data'][$personnelNumber] = $row;

            // Aktualisiere oder erstelle Mitarbeiter in der Übersicht
            $employeeExists = false;
            foreach ($db['employees'] as &$employee) {
                if ($employee['personnelNumber'] === $personnelNumber) {
                    // Aktualisiere Mitarbeiterdaten
                    if (isset($row['Mitarbeiter']) && !empty($row['Mitarbeiter'])) {
                        $employee['name'] = $row['Mitarbeiter'];
                    }

                    if (isset($row['Abteilung']) && !empty($row['Abteilung'])) {
                        $employee['department'] = $row['Abteilung'];
                    }

                    // Marktnummer mit Präfix "5" versehen, falls nicht bereits vorhanden
                    if (isset($row['Markt/ Einheit']) && !empty($row['Markt/ Einheit'])) {
                        $marketNumber = $row['Markt/ Einheit'];
                        if (substr($marketNumber, 0, 1) !== '5') {
                            $marketNumber = '5' . $marketNumber;
                        }
                        $employee['marketNumber'] = $marketNumber;
                    }

                    // Weitere Felder aktualisieren
                    $additionalFields = [
                        'birthdate' => ['Geburtsdatum'],
                        'entryDate' => ['Eintrittsdatum', 'Eintrittsdatum(anrechenbar)'],
                        'exitDate' => ['Austrittsdatum'],
                        'position' => ['Position', 'Stelle'],
                        'zipCity' => ['PLZ Ort', 'PLZ/Ort'],
                        'address' => ['Adresse', 'Straße'],
                        'phone' => ['Telefon', 'Telefonnummer'],
                        'mobile' => ['Mobil', 'Priv Handynr']
                    ];

                    foreach ($additionalFields as $field => $possibleColumns) {
                        foreach ($possibleColumns as $column) {
                            if (isset($row[$column]) && !empty($row[$column])) {
                                $employee[$field] = $row[$column];
                                break;
                            }
                        }
                    }

                    // Berechne Status basierend auf Ein- und Austrittsdatum
                    if (isset($employee['entryDate']) || isset($employee['exitDate'])) {
                        $status = 'aktiv'; // Standardwert

                        if (!isset($employee['entryDate']) || empty($employee['entryDate'])) {
                            $status = 'zu klären';
                        } elseif (isset($employee['exitDate']) && !empty($employee['exitDate'])) {
                            try {
                                $dateParts = explode('.', $employee['exitDate']);
                                if (count($dateParts) === 3) {
                                    $exitDate = new DateTime($dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0]);
                                    $today = new DateTime();
                                    $today->setTime(0, 0, 0);

                                    if ($exitDate < $today) {
                                        $status = 'austritt';
                                    } elseif ($exitDate >= $today) {
                                        $status = 'im austritt';
                                    }
                                }
                            } catch (Exception $e) {
                                error_log("Fehler bei der Datumskonvertierung für $personnelNumber: " . $e->getMessage());
                                $status = 'zu klären';
                            }
                        }

                        $employee['status'] = $status;
                    }

                    $employeeExists = true;
                    $updatedEmployees++;
                    break;
                }
            }

            if (!$employeeExists) {
                // Erstelle neuen Mitarbeiter
                $newId = 1;
                if (!empty($db['employees'])) {
                    $maxId = max(array_column($db['employees'], 'id'));
                    $newId = $maxId + 1;
                }

                // Marktnummer mit Präfix "5" versehen, falls nicht bereits vorhanden
                $marketNumber = '';
                if (isset($row['Markt/ Einheit']) && !empty($row['Markt/ Einheit'])) {
                    $marketNumber = $row['Markt/ Einheit'];
                    if (substr($marketNumber, 0, 1) !== '5') {
                        $marketNumber = '5' . $marketNumber;
                    }
                }

                // Sammle alle verfügbaren Daten für den neuen Mitarbeiter
                $newEmployee = [
                    'id' => $newId,
                    'personnelNumber' => $personnelNumber,
                    'name' => $row['Mitarbeiter'] ?? '',
                    'department' => $row['Abteilung'] ?? '',
                    'location' => '',
                    'marketNumber' => $marketNumber,
                    'workArea' => '' // Einsatzbereich immer leer lassen, nur manuelle Einträge erlaubt
                ];

                // Füge weitere Felder hinzu, wenn vorhanden
                $additionalFields = [
                    'birthdate' => ['Geburtsdatum'],
                    'entryDate' => ['Eintrittsdatum', 'Eintrittsdatum(anrechenbar)'],
                    'exitDate' => ['Austrittsdatum'],
                    'position' => ['Position', 'Stelle'],
                    'zipCity' => ['PLZ Ort', 'PLZ/Ort'],
                    'address' => ['Adresse', 'Straße'],
                    'phone' => ['Telefon', 'Telefonnummer'],
                    'mobile' => ['Mobil', 'Priv Handynr']
                ];

                foreach ($additionalFields as $field => $possibleColumns) {
                    foreach ($possibleColumns as $column) {
                        if (isset($row[$column]) && !empty($row[$column])) {
                            $newEmployee[$field] = $row[$column];
                            break;
                        }
                    }
                }

                // Berechne Status basierend auf Ein- und Austrittsdatum
                $status = 'aktiv'; // Standardwert

                if (!isset($newEmployee['entryDate']) || empty($newEmployee['entryDate'])) {
                    $status = 'zu klären';
                } elseif (isset($newEmployee['exitDate']) && !empty($newEmployee['exitDate'])) {
                    try {
                        $dateParts = explode('.', $newEmployee['exitDate']);
                        if (count($dateParts) === 3) {
                            $exitDate = new DateTime($dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0]);
                            $today = new DateTime();
                            $today->setTime(0, 0, 0);

                            if ($exitDate < $today) {
                                $status = 'austritt';
                            } elseif ($exitDate >= $today) {
                                $status = 'im austritt';
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Fehler bei der Datumskonvertierung für neuen Mitarbeiter $personnelNumber: " . $e->getMessage());
                        $status = 'zu klären';
                    }
                }

                $newEmployee['status'] = $status;

                $db['employees'][] = $newEmployee;
                error_log("Neuer Mitarbeiter erstellt: $personnelNumber, Name: " . ($row['Mitarbeiter'] ?? 'Unbekannt'));
                $newEmployees++;
            }

            // Die Mitarbeiteraktualisierungslogik wurde bereits oben implementiert
        }
    } elseif ($type === 'salary_data') {
        // Verarbeite Gehaltsdaten
        error_log("Verarbeite Gehaltsdaten");

        // Überprüfe die Spaltennamen und versuche, die richtigen Spalten zu finden
        $firstRow = $data[0] ?? [];
        error_log("Verfügbare Spalten: " . implode(", ", array_keys($firstRow)));

        // Versuche, die Spalte für die Personalnummer zu finden
        $personnelNumberKey = null;
        $possibleKeys = ['personnelNumber', 'PersNr', 'PersNR', 'Personalnummer', 'Personal-Nr'];
        foreach ($possibleKeys as $key) {
            if (isset($firstRow[$key])) {
                $personnelNumberKey = $key;
                error_log("Personalnummer-Spalte gefunden: $key");
                break;
            }
        }

        if (!$personnelNumberKey) {
            error_log("FEHLER: Keine Spalte für Personalnummer gefunden!");
            return false;
        }

        // Versuche, andere wichtige Spalten zu finden
        $nameKey = null;
        $possibleNameKeys = ['name', 'Name', 'Mitarbeiter', 'MitarbeiterName'];
        foreach ($possibleNameKeys as $key) {
            if (isset($firstRow[$key])) {
                $nameKey = $key;
                error_log("Name-Spalte gefunden: $key");
                break;
            }
        }

        $departmentKey = null;
        $possibleDeptKeys = ['department', 'Department', 'Abteilung'];
        foreach ($possibleDeptKeys as $key) {
            if (isset($firstRow[$key])) {
                $departmentKey = $key;
                error_log("Abteilungs-Spalte gefunden: $key");
                break;
            }
        }

        $costCenterKey = null;
        $possibleCCKeys = ['costCenter', 'CostCenter', 'Kostenstelle', 'Marktnummer'];
        foreach ($possibleCCKeys as $key) {
            if (isset($firstRow[$key])) {
                $costCenterKey = $key;
                error_log("Kostenstellen-Spalte gefunden: $key");
                break;
            }
        }

        foreach ($data as $index => $row) {
            // Debug: Zeige die ersten paar Zeilen
            if ($index < 3) {
                error_log("Zeile $index: " . json_encode($row));
            }

            $personnelNumber = $row[$personnelNumberKey] ?? '';
            if (empty($personnelNumber)) {
                error_log("Überspringe Zeile $index: Keine Personalnummer gefunden");
                $skippedRows++;
                continue;
            }

            // Validiere wichtige Felder
            $validationFailed = false;

            // Validiere Datumswerte
            $dateFields = ['Eintrittsdatum', 'Eintrittsdatum(anrechenbar)', 'Austrittsdatum', 'Geburtsdatum'];
            foreach ($dateFields as $field) {
                if (isset($row[$field]) && !empty($row[$field]) && !validateDate($row[$field])) {
                    error_log("Validierungsfehler in Zeile $index: Ungültiges Datum in Feld '$field': " . $row[$field]);
                    // Wir setzen validationFailed nicht auf true, um den Import nicht zu blockieren
                    // Stattdessen korrigieren wir das Datum oder ignorieren es
                    $row[$field] = ''; // Leeres Datum ist besser als ungültiges Datum
                }
            }

            // Validiere numerische Werte
            $numericFields = [
                'Wochen-Stunden', 'Soll-Stunden', 'Stunden bezahlt', 'Mehrarbeitsstd. bez.',
                'vertragliche Bezuege', 'Basisbezuege vertraglich', 'Zulagen Gesamt vertraglich',
                'Funktionszulagen vertraglich', 'freiwillige Zulagen vertraglich',
                'Mehrarbeitspauschale vertraglich', 'Zulage Kasse vertraglich',
                'sonstige Zulagen vertraglich', 'bezahlte Fehlzeiten', 'AG-SV-Gesamt',
                'Gesamtkosten', 'Brutto Std-Lohn', 'Gesamtkosten/ h'
            ];

            foreach ($numericFields as $field) {
                if (isset($row[$field]) && !empty($row[$field]) && !validateNumeric($row[$field])) {
                    error_log("Validierungsfehler in Zeile $index: Ungültiger numerischer Wert in Feld '$field': " . $row[$field]);
                    // Wir setzen validationFailed nicht auf true, um den Import nicht zu blockieren
                    // Stattdessen korrigieren wir den Wert oder ignorieren ihn
                    $row[$field] = '0'; // 0 ist besser als ungültiger Wert
                }
            }

            // Wir überspringen die Zeile nur bei schwerwiegenden Fehlern
            if ($validationFailed) {
                error_log("Überspringe Zeile $index wegen schwerwiegender Validierungsfehler");
                $validationErrors++;
                continue;
            }

            // Füge Monat_Jahr hinzu, falls nicht vorhanden
            if (!isset($row['Monat_Jahr']) || empty($row['Monat_Jahr'])) {
                $row['Monat_Jahr'] = sprintf("%02d.%04d", intval($month), intval($year));
                error_log("Monat_Jahr für Zeile $index hinzugefügt: " . $row['Monat_Jahr']);
            }

            // Stelle sicher, dass PersNr gesetzt ist
            if (!isset($row['PersNr']) || empty($row['PersNr'])) {
                $row['PersNr'] = $personnelNumber;
                error_log("PersNr für Zeile $index hinzugefügt: " . $row['PersNr']);
            }

            // Generiere einen eindeutigen Schlüssel für die Zeile
            static $counter = 0;
            $uniqueKey = $personnelNumber . '_' . sprintf("%02d%04d", intval($month), intval($year)) . '_' . $counter++;

            // Speichere die Gehaltsdaten
            error_log("Speichere Gehaltsdaten für $personnelNumber (Zeile $index, Monat_Jahr: " . $row['Monat_Jahr'] . ", Schlüssel: $uniqueKey)");
            $db['salary_data'][$uniqueKey] = $row;

            // Debug-Ausgabe: Zeige alle Gehaltsdaten für diesen Mitarbeiter
            $employeeData = [];
            foreach ($db['salary_data'] as $key => $data) {
                if (strpos($key, $personnelNumber . '_') === 0 ||
                    (isset($data['PersNr']) && $data['PersNr'] == $personnelNumber)) {
                    if (isset($data['Monat_Jahr'])) {
                        $employeeData[] = $data['Monat_Jahr'] . " (Schlüssel: " . $key . ")";
                    } else {
                        $employeeData[] = 'undefined' . " (Schlüssel: " . $key . ")";
                    }
                }
            }
            error_log("Alle Gehaltsdaten für $personnelNumber nach Import: " . implode(", ", $employeeData));

            // Sammle alle Gehaltsdaten für diesen Mitarbeiter, um den neuesten Datensatz zu finden
            $allSalaryData = [];
            foreach ($db['salary_data'] as $key => $data) {
                if (strpos($key, $personnelNumber . '_') === 0 ||
                    (isset($data['PersNr']) && $data['PersNr'] == $personnelNumber)) {
                    $allSalaryData[] = $data;
                }
            }

            // Sortiere die Gehaltsdaten nach Monat_Jahr (neueste zuerst)
            if (!empty($allSalaryData)) {
                usort($allSalaryData, function($a, $b) {
                    if (isset($a['Monat_Jahr']) && isset($b['Monat_Jahr'])) {
                        // Konvertiere Monat_Jahr (MM.YYYY) in ein sortierbares Format
                        $monthYearA = explode('.', $a['Monat_Jahr']);
                        $monthYearB = explode('.', $b['Monat_Jahr']);

                        if (count($monthYearA) == 2 && count($monthYearB) == 2) {
                            $yearA = intval($monthYearA[1]);
                            $monthA = intval($monthYearA[0]);
                            $yearB = intval($monthYearB[1]);
                            $monthB = intval($monthYearB[0]);

                            // Vergleiche zuerst die Jahre, dann die Monate
                            if ($yearA != $yearB) {
                                return $yearB - $yearA; // Neuestes Jahr zuerst
                            }
                            return $monthB - $monthA; // Neuester Monat zuerst
                        }
                    }
                    return 0;
                });
            }

            // Verwende den neuesten Datensatz für die Mitarbeiterinformationen
            $latestSalaryData = !empty($allSalaryData) ? $allSalaryData[0] : $row;

            // Aktualisiere Mitarbeiter in der Übersicht, falls nötig
            $employeeExists = false;
            foreach ($db['employees'] as &$employee) {
                if ($employee['personnelNumber'] === $personnelNumber) {
                    // Aktualisiere nur, wenn Daten vorhanden sind
                    if ($departmentKey && isset($row[$departmentKey]) && !empty($row[$departmentKey])) {
                        $employee['department'] = $row[$departmentKey];
                        error_log("Aktualisiere Abteilung für $personnelNumber: " . $row[$departmentKey]);
                    }

                    // Marktnummer mit Präfix "5" versehen, falls nicht bereits vorhanden
                    if ($costCenterKey && isset($row[$costCenterKey]) && !empty($row[$costCenterKey])) {
                        $marketNumber = $row[$costCenterKey];
                        if (substr($marketNumber, 0, 1) !== '5') {
                            $marketNumber = '5' . $marketNumber;
                        }
                        $employee['marketNumber'] = $marketNumber;
                        error_log("Aktualisiere Marktnummer für $personnelNumber: $marketNumber");
                    }

                    // Einsatzbereich nicht automatisch aktualisieren, nur manuelle Einträge erlauben
                    // Wenn ein Einsatzbereich explizit in der Spalte "Einsatzbereich" angegeben ist, könnte man ihn hier übernehmen
                    if (isset($row['Einsatzbereich']) && !empty($row['Einsatzbereich'])) {
                        $employee['workArea'] = $row['Einsatzbereich'];
                        error_log("Aktualisiere Einsatzbereich für $personnelNumber aus explizitem Einsatzbereich-Feld: " . $row['Einsatzbereich']);
                    }

                    // Aktualisiere weitere Felder, wenn vorhanden
                    $additionalFields = [
                        'birthdate' => ['Geburtsdatum'],
                        'entryDate' => ['Eintrittsdatum', 'Eintrittsdatum(anrechenbar)'],
                        'exitDate' => ['Austrittsdatum'],
                        'position' => ['Position', 'Stelle'],
                        'zipCity' => ['PLZ Ort', 'PLZ/Ort'],
                        'address' => ['Adresse', 'Straße'],
                        'phone' => ['Telefon', 'Telefonnummer'],
                        'mobile' => ['Mobil', 'Priv Handynr']
                    ];

                    foreach ($additionalFields as $field => $possibleColumns) {
                        foreach ($possibleColumns as $column) {
                            if (isset($row[$column]) && !empty($row[$column])) {
                                $employee[$field] = $row[$column];
                                error_log("Aktualisiere $field für $personnelNumber: " . $row[$column]);
                                break;
                            }
                        }
                    }

                    // Verwende den neuesten Datensatz für die Statusberechnung
                    $status = 'inaktiv'; // Standardwert, wenn kein Eintrittsdatum vorhanden ist

                    // Verwende Austrittsdatum und Eintrittsdatum aus dem neuesten Datensatz
                    $exitDate = '';
                    $entryDate = '';

                    // Prüfe, ob wir Daten aus dem neuesten Datensatz haben
                    if (isset($latestSalaryData['Austrittsdatum']) && !empty($latestSalaryData['Austrittsdatum'])) {
                        $exitDate = $latestSalaryData['Austrittsdatum'];
                        error_log("Verwende Austrittsdatum aus neuestem Datensatz für $personnelNumber: $exitDate");
                    } else if (isset($employee['exitDate']) && !empty($employee['exitDate'])) {
                        $exitDate = $employee['exitDate'];
                    }

                    if (isset($latestSalaryData['Eintrittsdatum(anrechenbar)']) && !empty($latestSalaryData['Eintrittsdatum(anrechenbar)'])) {
                        $entryDate = $latestSalaryData['Eintrittsdatum(anrechenbar)'];
                        error_log("Verwende Eintrittsdatum aus neuestem Datensatz für $personnelNumber: $entryDate");
                    } else if (isset($employee['entryDate']) && !empty($employee['entryDate'])) {
                        $entryDate = $employee['entryDate'];
                    }

                    // Aktualisiere die Daten im Mitarbeiterobjekt
                    if (!empty($exitDate)) {
                        $employee['exitDate'] = $exitDate;
                    }

                    if (!empty($entryDate)) {
                        $employee['entryDate'] = $entryDate;
                    }

                    // Führende Logik: Wenn Austrittsdatum vorhanden und gültig, Status = Austritt
                    if (!empty($exitDate)) {
                        try {
                            $exitDateParts = explode('.', $exitDate);
                            if (count($exitDateParts) === 3) {
                                // Gültiges Austrittsdatum im Format TT.MM.JJJJ
                                $status = 'austritt';
                                error_log("Setze Status für $personnelNumber auf 'austritt', da Austrittsdatum $exitDate vorhanden ist");
                            }
                        } catch (Exception $e) {
                            error_log("Fehler bei der Datumskonvertierung des Austrittsdatums für $personnelNumber: " . $e->getMessage());
                        }
                    }
                    // Wenn kein Austrittsdatum, aber Eintrittsdatum vorhanden, Status = Aktiv
                    else if (!empty($entryDate)) {
                        $status = 'aktiv';
                        error_log("Setze Status für $personnelNumber auf 'aktiv', da Eintrittsdatum $entryDate vorhanden ist");
                    } else {
                        error_log("Setze Status für $personnelNumber auf 'inaktiv', da kein Eintrittsdatum vorhanden ist");
                    }

                    $employee['status'] = $status;
                    error_log("Endgültiger Status für $personnelNumber: $status");

                    $employeeExists = true;
                    $updatedEmployees++;
                    break;
                }
            }

            if (!$employeeExists && $nameKey && isset($row[$nameKey])) {
                // Erstelle neuen Mitarbeiter, falls er noch nicht existiert
                $newId = 1;
                if (!empty($db['employees'])) {
                    $maxId = max(array_column($db['employees'], 'id'));
                    $newId = $maxId + 1;
                }

                // Verwende den neuesten Datensatz für die Mitarbeiterinformationen

                // Marktnummer mit Präfix "5" versehen, falls nicht bereits vorhanden
                $marketNumber = '';
                if (isset($latestSalaryData['Kostenstelle']) && !empty($latestSalaryData['Kostenstelle'])) {
                    $marketNumber = $latestSalaryData['Kostenstelle'];
                    if (substr($marketNumber, 0, 1) !== '5') {
                        $marketNumber = '5' . $marketNumber;
                    }
                    error_log("Marktnummer aus neuestem Datensatz für $personnelNumber: $marketNumber");
                } else if ($costCenterKey && isset($row[$costCenterKey]) && !empty($row[$costCenterKey])) {
                    $marketNumber = $row[$costCenterKey];
                    if (substr($marketNumber, 0, 1) !== '5') {
                        $marketNumber = '5' . $marketNumber;
                    }
                }

                // Einsatzbereich nicht automatisch übernehmen, nur manuelle Einträge erlauben
                $workArea = '';
                // Wenn ein Einsatzbereich explizit in der Spalte "Einsatzbereich" angegeben ist, könnte man ihn hier übernehmen
                if (isset($row['Einsatzbereich']) && !empty($row['Einsatzbereich'])) {
                    $workArea = $row['Einsatzbereich'];
                }

                // Sammle alle verfügbaren Daten für den neuen Mitarbeiter
                $newEmployee = [
                    'id' => $newId,
                    'personnelNumber' => $personnelNumber,
                    'name' => isset($latestSalaryData['Mitarbeiter']) ? $latestSalaryData['Mitarbeiter'] : ($row[$nameKey] ?? ''),
                    'department' => isset($latestSalaryData['Abteilung']) ? $latestSalaryData['Abteilung'] : ($departmentKey && isset($row[$departmentKey]) ? $row[$departmentKey] : ''),
                    'location' => '',
                    'marketNumber' => $marketNumber,
                    'workArea' => $workArea
                ];

                // Füge weitere Felder hinzu, wenn vorhanden
                $additionalFields = [
                    'birthdate' => ['Geburtsdatum'],
                    'entryDate' => ['Eintrittsdatum', 'Eintrittsdatum(anrechenbar)'],
                    'exitDate' => ['Austrittsdatum'],
                    'position' => ['Position', 'Stelle'],
                    'zipCity' => ['PLZ Ort', 'PLZ/Ort'],
                    'address' => ['Adresse', 'Straße'],
                    'phone' => ['Telefon', 'Telefonnummer'],
                    'mobile' => ['Mobil', 'Priv Handynr']
                ];

                foreach ($additionalFields as $field => $possibleColumns) {
                    foreach ($possibleColumns as $column) {
                        if (isset($row[$column]) && !empty($row[$column])) {
                            $newEmployee[$field] = $row[$column];
                            break;
                        }
                    }
                }

                // Verwende den neuesten Datensatz für die Statusberechnung
                $status = 'inaktiv'; // Standardwert, wenn kein Eintrittsdatum vorhanden ist

                // Verwende Austrittsdatum und Eintrittsdatum aus dem neuesten Datensatz
                $exitDate = '';
                $entryDate = '';

                // Prüfe, ob wir Daten aus dem neuesten Datensatz haben
                if (isset($latestSalaryData['Austrittsdatum']) && !empty($latestSalaryData['Austrittsdatum'])) {
                    $exitDate = $latestSalaryData['Austrittsdatum'];
                    $newEmployee['exitDate'] = $exitDate;
                    error_log("Verwende Austrittsdatum aus neuestem Datensatz für neuen Mitarbeiter $personnelNumber: $exitDate");
                } else if (isset($newEmployee['exitDate']) && !empty($newEmployee['exitDate'])) {
                    $exitDate = $newEmployee['exitDate'];
                }

                if (isset($latestSalaryData['Eintrittsdatum(anrechenbar)']) && !empty($latestSalaryData['Eintrittsdatum(anrechenbar)'])) {
                    $entryDate = $latestSalaryData['Eintrittsdatum(anrechenbar)'];
                    $newEmployee['entryDate'] = $entryDate;
                    error_log("Verwende Eintrittsdatum aus neuestem Datensatz für neuen Mitarbeiter $personnelNumber: $entryDate");
                } else if (isset($newEmployee['entryDate']) && !empty($newEmployee['entryDate'])) {
                    $entryDate = $newEmployee['entryDate'];
                }

                // Führende Logik: Wenn Austrittsdatum vorhanden und gültig, Status = Austritt
                if (!empty($exitDate)) {
                    try {
                        $exitDateParts = explode('.', $exitDate);
                        if (count($exitDateParts) === 3) {
                            // Gültiges Austrittsdatum im Format TT.MM.JJJJ
                            $status = 'austritt';
                            error_log("Setze Status für neuen Mitarbeiter $personnelNumber auf 'austritt', da Austrittsdatum $exitDate vorhanden ist");
                        }
                    } catch (Exception $e) {
                        error_log("Fehler bei der Datumskonvertierung des Austrittsdatums für neuen Mitarbeiter $personnelNumber: " . $e->getMessage());
                    }
                }
                // Wenn kein Austrittsdatum, aber Eintrittsdatum vorhanden, Status = Aktiv
                else if (!empty($entryDate)) {
                    $status = 'aktiv';
                    error_log("Setze Status für neuen Mitarbeiter $personnelNumber auf 'aktiv', da Eintrittsdatum $entryDate vorhanden ist");
                } else {
                    error_log("Setze Status für neuen Mitarbeiter $personnelNumber auf 'inaktiv', da kein Eintrittsdatum vorhanden ist");
                }

                $newEmployee['status'] = $status;

                $db['employees'][] = $newEmployee;
                error_log("Neuer Mitarbeiter erstellt: $personnelNumber, Name: " . ($row[$nameKey] ?? 'Unbekannt') . ", Status: $status");
                $newEmployees++;
            }
        }
    }

    // Statistik-Zusammenfassung
    error_log("Import abgeschlossen: $newEmployees neue Mitarbeiter, $updatedEmployees aktualisierte Mitarbeiter, $skippedRows übersprungene Zeilen, $validationErrors Validierungsfehler");

    // Aktualisiere den Import-Status
    updateImportStatus($year, $month, $type, true);

    // Speichere die Änderungen in der Datenbank
    $saveResult = saveDatabase();
    error_log("Datenbank nach Import gespeichert: " . ($saveResult ? "Erfolgreich" : "Fehlgeschlagen"));

    // Zeige aktuelle Mitarbeiterzahl und Statistiken
    error_log("Aktuelle Mitarbeiterzahl: " . count($db['employees']));
    error_log("Aktuelle Gehaltsdaten: " . count($db['salary_data']));

    // Zähle aktive Mitarbeiter
    $activeCount = 0;
    foreach ($db['employees'] as $employee) {
        if (isset($employee['status']) && $employee['status'] === 'aktiv') {
            $activeCount++;
        }
    }
    error_log("Aktive Mitarbeiter: $activeCount");

    return true;
}

// Funktion zum Abrufen der importierten Daten für die Vorschau
function getImportedData($year, $month, $type) {
    global $db;

    $result = [];

    if ($type === 'employee_data') {
        // Für Stammdaten: Alle Spalten aus den importierten Daten extrahieren
        $allColumns = [];
        $allData = [];

        // Sammle alle Daten und Spalten
        foreach ($db['employee_data'] as $personnelNumber => $data) {
            // Stelle sicher, dass die Daten für den angegebenen Monat/Jahr sind
            if (isset($data['Monat_Jahr'])) {
                $monthYearParts = explode('.', $data['Monat_Jahr']);
                if (count($monthYearParts) == 2) {
                    $dataMonth = $monthYearParts[0];
                    $dataYear = $monthYearParts[1];

                    // Wenn kein spezifischer Monat/Jahr angefordert wurde oder die Daten zum angeforderten Monat/Jahr passen
                    if (($month === '' && $year === '') || ($dataMonth === $month && $dataYear === $year)) {
                        $allData[] = $data;
                        foreach (array_keys($data) as $column) {
                            if (!in_array($column, $allColumns)) {
                                $allColumns[] = $column;
                            }
                        }
                    }
                } else {
                    // Wenn Monat_Jahr nicht im erwarteten Format ist, füge die Daten trotzdem hinzu
                    $allData[] = $data;
                    foreach (array_keys($data) as $column) {
                        if (!in_array($column, $allColumns)) {
                            $allColumns[] = $column;
                        }
                    }
                }
            } else {
                // Wenn Monat_Jahr nicht gesetzt ist, füge die Daten trotzdem hinzu
                $allData[] = $data;
                foreach (array_keys($data) as $column) {
                    if (!in_array($column, $allColumns)) {
                        $allColumns[] = $column;
                    }
                }
            }
        }

        // Sortiere die Spalten in eine sinnvolle Reihenfolge
        $orderedColumns = [];
        $priorityColumns = ['Monat_Jahr', 'PersNr', 'Mitarbeiter', 'Stelle', 'Markt/ Einheit', 'Abteilung'];

        // Füge zuerst die Prioritätsspalten hinzu
        foreach ($priorityColumns as $column) {
            if (in_array($column, $allColumns)) {
                $orderedColumns[] = $column;
                $key = array_search($column, $allColumns);
                if ($key !== false) {
                    unset($allColumns[$key]);
                }
            }
        }

        // Füge dann die restlichen Spalten hinzu
        $orderedColumns = array_merge($orderedColumns, $allColumns);

        // Erstelle das Ergebnis mit allen Spalten
        $result['columns'] = $orderedColumns;
        $result['data'] = $allData;

        error_log("Mitarbeiterdaten für Vorschau: " . count($allData) . " Datensätze");

        return $result;
    } elseif ($type === 'salary_data') {
        // Für Gehaltsdaten: Alle Spalten aus den importierten Daten extrahieren
        $allColumns = [];
        $allData = [];
        $targetMonthYear = $month . '.' . $year;

        error_log("Suche nach Gehaltsdaten für Monat/Jahr: $targetMonthYear");

        // Sammle alle Daten und Spalten
        foreach ($db['salary_data'] as $key => $data) {
            // Stelle sicher, dass die Daten für den angegebenen Monat/Jahr sind
            if (isset($data['Monat_Jahr'])) {
                // Debug-Ausgabe für die ersten 10 Datensätze
                static $debugCount = 0;
                if ($debugCount < 10) {
                    error_log("Prüfe Datensatz: " . $data['Monat_Jahr'] . " für PersNr: " . ($data['PersNr'] ?? 'unbekannt'));
                    $debugCount++;
                }

                // Exakter Vergleich mit dem Ziel-Monat/Jahr
                if ($data['Monat_Jahr'] === $targetMonthYear) {
                    error_log("Gefunden: Datensatz für " . $data['Monat_Jahr'] . ", PersNr: " . ($data['PersNr'] ?? 'unbekannt'));
                    $allData[] = $data;
                    foreach (array_keys($data) as $column) {
                        if (!in_array($column, $allColumns)) {
                            $allColumns[] = $column;
                        }
                    }
                }
            }
        }

        error_log("Gefundene Datensätze für $targetMonthYear: " . count($allData));

        // Sortiere die Spalten in eine sinnvolle Reihenfolge
        $orderedColumns = [];
        $priorityColumns = ['Monat_Jahr', 'PersNr', 'Mitarbeiter', 'Stelle', 'Kostenstelle', 'Abteilung'];

        // Füge zuerst die Prioritätsspalten hinzu
        foreach ($priorityColumns as $column) {
            if (in_array($column, $allColumns)) {
                $orderedColumns[] = $column;
                $key = array_search($column, $allColumns);
                if ($key !== false) {
                    unset($allColumns[$key]);
                }
            }
        }

        // Füge dann die restlichen Spalten hinzu
        $orderedColumns = array_merge($orderedColumns, $allColumns);

        // Erstelle das Ergebnis mit allen Spalten
        $result['columns'] = $orderedColumns;
        $result['data'] = $allData;

        error_log("Gehaltsdaten für Vorschau: " . count($allData) . " Datensätze für Monat/Jahr: $month.$year");

        return $result;
    }

    return [];
}

// Funktion zum Abrufen der Dashboard-Daten
function getDashboardData() {
    global $db;

    // Zähle Mitarbeiter nach Status
    $statusCounts = [
        'aktiv' => 0,
        'im austritt' => 0,
        'austritt' => 0,
        'zu klären' => 0,
        'total' => count($db['employees'])
    ];

    foreach ($db['employees'] as $employee) {
        $status = $employee['status'] ?? 'zu klären';
        if (isset($statusCounts[$status])) {
            $statusCounts[$status]++;
        }
    }

    // Zähle Mitarbeiter nach Abteilung
    $departmentCounts = [];
    foreach ($db['employees'] as $employee) {
        $department = $employee['department'] ?? 'Unbekannt';
        if (!isset($departmentCounts[$department])) {
            $departmentCounts[$department] = 0;
        }
        $departmentCounts[$department]++;
    }

    // Zähle Mitarbeiter nach Marktnummer
    $marketCounts = [];
    foreach ($db['employees'] as $employee) {
        $market = $employee['marketNumber'] ?? 'Unbekannt';
        if (!isset($marketCounts[$market])) {
            $marketCounts[$market] = 0;
        }
        $marketCounts[$market]++;
    }

    return [
        'status' => $statusCounts,
        'departments' => $departmentCounts,
        'markets' => $marketCounts,
        'total_employees' => count($db['employees']),
        'total_employee_data' => count($db['employee_data']),
        'total_salary_data' => count($db['salary_data'])
    ];
}

// Funktion zum Löschen von Importdaten
function deleteImport($type, $year, $month) {
    global $db;

    try {
        if ($type === 'employee_data') {
            // Lösche nur die Mitarbeiterdaten für diesen Monat/Jahr
            $monthYearPattern = $month . '.' . $year;
            $keysToDelete = [];

            foreach ($db['employee_data'] as $key => $data) {
                if (isset($data['Monat_Jahr']) && $data['Monat_Jahr'] === $monthYearPattern) {
                    $keysToDelete[] = $key;
                    error_log("Markiere Mitarbeiterdaten für Löschung: $key (Monat_Jahr: $monthYearPattern)");
                }
            }

            foreach ($keysToDelete as $key) {
                unset($db['employee_data'][$key]);
            }

            error_log("Mitarbeiterdaten für $month.$year gelöscht: " . count($keysToDelete) . " Einträge");

            // Wir löschen die Mitarbeiter nicht mehr, da sie auch in Gehaltsdaten existieren können
            // Stattdessen behalten wir alle Mitarbeiter bei
            error_log("Mitarbeiterdaten für $month.$year gelöscht, aber Mitarbeiter in der Übersicht beibehalten");
        } elseif ($type === 'salary_data') {
            // Lösche nur die Gehaltsdaten für diesen Monat/Jahr
            $monthYearPattern = $month . '.' . $year;
            $keysToDelete = [];

            foreach ($db['salary_data'] as $key => $data) {
                if (isset($data['Monat_Jahr']) && $data['Monat_Jahr'] === $monthYearPattern) {
                    $keysToDelete[] = $key;
                    error_log("Markiere Gehaltsdaten für Löschung: $key (Monat_Jahr: $monthYearPattern)");
                }
            }

            foreach ($keysToDelete as $key) {
                unset($db['salary_data'][$key]);
            }

            error_log("Gehaltsdaten für $month.$year gelöscht: " . count($keysToDelete) . " Einträge");
        }

        // Aktualisiere den Import-Status
        updateImportStatus($year, $month, $type, false);

        // Speichere die Änderungen in der Datenbank
        $saveResult = saveDatabase();
        error_log("Datenbank nach Löschen gespeichert: " . ($saveResult ? "Erfolgreich" : "Fehlgeschlagen"));

        // Zeige aktuelle Mitarbeiterzahl und Statistiken
        error_log("Aktuelle Mitarbeiterzahl nach Löschen: " . count($db['employees']));
        error_log("Aktuelle Gehaltsdaten nach Löschen: " . count($db['salary_data']));

        // Zähle aktive Mitarbeiter
        $activeCount = 0;
        foreach ($db['employees'] as $employee) {
            if (isset($employee['status']) && $employee['status'] === 'aktiv') {
                $activeCount++;
            }
        }
        error_log("Aktive Mitarbeiter nach Löschen: $activeCount");

        return true;
    } catch (Exception $e) {
        error_log("Fehler beim Löschen von Importdaten: " . $e->getMessage());
        return false;
    }
}

// Handle API requests
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// CORS Headers
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($requestMethod === 'OPTIONS') {
    exit(0);
}

// API-Endpunkt für Abteilungen
if (strpos($requestUri, '/api/departments') !== false && $requestMethod === 'GET') {
    header('Content-Type: application/json');

    $departments = [];
    foreach ($db['employees'] as $employee) {
        if (!empty($employee['department']) && !in_array($employee['department'], $departments)) {
            $departments[] = $employee['department'];
        }
    }
    sort($departments);

    echo json_encode($departments);
    exit;
}

// API-Endpunkt für Marktnummern
if (strpos($requestUri, '/api/market-numbers') !== false && $requestMethod === 'GET') {
    header('Content-Type: application/json');

    $marketNumbers = [];
    foreach ($db['employees'] as $employee) {
        if (!empty($employee['marketNumber']) && !in_array($employee['marketNumber'], $marketNumbers)) {
            $marketNumbers[] = $employee['marketNumber'];
        }
    }
    sort($marketNumbers);

    echo json_encode($marketNumbers);
    exit;
}

// API-Endpunkt für Mitarbeiter
if (strpos($requestUri, '/api/employees') !== false) {
    header('Content-Type: application/json');

    if ($requestMethod === 'GET') {
        // Hole alle Mitarbeiter mit Details
        $employees = [];
        foreach ($db['employees'] as $employee) {
            $details = getEmployeeDetails($employee['id']);
            if ($details) {
                $employeeData = $details['basic'];
                $employeeData['salary_data'] = $details['salary_data'];
                $employeeData['employee_data'] = $details['employee_data'];
                $employees[] = $employeeData;
            } else {
                $employees[] = $employee;
            }
        }

        echo json_encode($employees);
        exit;
    } elseif ($requestMethod === 'PUT') {
        // Update Mitarbeiter
        $data = json_decode(file_get_contents('php://input'), true);
        $id = $data['id'] ?? null;

        if ($id && updateEmployee($id, $data)) {
            echo json_encode(['success' => true]);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Fehler beim Aktualisieren des Mitarbeiters']);
        }
        exit;
    }
}

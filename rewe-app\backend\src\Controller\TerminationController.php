<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;
use PhpOffice\PhpWord\Shared\Converter;

class TerminationController extends AbstractController
{
    #[Route('/api/termination/test', name: 'termination_test', methods: ['GET'])]
    public function test(): JsonResponse
    {
        return new JsonResponse(['status' => 'Backend läuft', 'phpword' => class_exists('PhpOffice\PhpWord\PhpWord')]);
    }

    #[Route('/api/termination/generate', name: 'termination_generate', methods: ['POST'])]
    public function generateTermination(Request $request): Response
    {
        try {
            $data = json_decode($request->getContent(), true);

            // Debug-Ausgabe
            error_log('Received data: ' . print_r($data, true));

            // Validierung der Eingabedaten
            if (!isset($data['employeeName']) || !isset($data['terminationType']) || !isset($data['signatory'])) {
                return new JsonResponse(['error' => 'Fehlende Pflichtfelder: ' . json_encode($data)], 400);
            }

            // Prüfe ob PhpWord verfügbar ist
            if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
                return new JsonResponse(['error' => 'PhpWord ist nicht installiert'], 500);
            }

            // Erstelle ein neues Word-Dokument
            $phpWord = new PhpWord();

            // Setze deutsche Sprache
            $phpWord->getSettings()->setThemeFontLang(new \PhpOffice\PhpWord\Style\Language('de-DE'));

            // Füge eine Sektion hinzu
            $section = $phpWord->addSection([
                'marginTop' => Converter::cmToTwip(2.5),
                'marginBottom' => Converter::cmToTwip(2.5),
                'marginLeft' => Converter::cmToTwip(2.5),
                'marginRight' => Converter::cmToTwip(2.5),
            ]);

            // Definiere Styles
            $phpWord->addFontStyle('headerStyle', [
                'name' => 'Arial',
                'size' => 14,
                'bold' => true,
                'color' => 'E30613' // REWE Rot
            ]);

            $phpWord->addFontStyle('normalStyle', [
                'name' => 'Arial',
                'size' => 11
            ]);

            $phpWord->addFontStyle('boldStyle', [
                'name' => 'Arial',
                'size' => 11,
                'bold' => true
            ]);

            // Header mit REWE Logo (falls vorhanden)
            $headerTable = $section->addTable();
            $headerRow = $headerTable->addRow();
            $logoCell = $headerRow->addCell(4000);

            // Versuche das Logo zu laden
            $logoPath = $this->getParameter('kernel.project_dir') . '/public/assets/LOGO-REWE-DUGA-1-300x293.png';
            if (file_exists($logoPath)) {
                $logoCell->addImage($logoPath, [
                    'width' => 100,
                    'height' => 100,
                    'positioning' => \PhpOffice\PhpWord\Style\Image::POSITION_RELATIVE,
                    'posHorizontal' => \PhpOffice\PhpWord\Style\Image::POSITION_HORIZONTAL_LEFT,
                    'posVertical' => \PhpOffice\PhpWord\Style\Image::POSITION_VERTICAL_TOP,
                ]);
            }

            $companyCell = $headerRow->addCell(4000);
            $companyCell->addText('REWE Markt GmbH', 'headerStyle');
            $companyCell->addTextBreak();
            $companyCell->addText($this->getMarketAddress($data), 'normalStyle');

            // Leerzeile
            $section->addTextBreak(2);

            // Empfängeradresse
            if (!empty($data['employeeAddress'])) {
                $section->addText($data['employeeName'], 'normalStyle');
                $section->addText($data['employeeAddress'], 'normalStyle');
                if (!empty($data['employeeZipCity'])) {
                    $section->addText($data['employeeZipCity'], 'normalStyle');
                }
                $section->addTextBreak(2);
            }

            // Datum und Ort
            $issueDate = new \DateTime($data['issueDate']);
            $section->addText('Bedburg, den ' . $issueDate->format('d.m.Y'), 'normalStyle', ['alignment' => 'right']);
            $section->addTextBreak(2);

            // Betreff
            $terminationTypeText = $this->getTerminationTypeText($data['terminationType']);
            $section->addText('Betreff: ' . $terminationTypeText, 'boldStyle');
            $section->addTextBreak(2);

            // Anrede
            $section->addText('Sehr geehrte/r ' . $data['employeeName'] . ',', 'normalStyle');
            $section->addTextBreak();

            // Kündigungstext basierend auf Typ
            $this->addTerminationText($section, $data);

            // Grußformel
            $section->addTextBreak();
            $section->addText('Mit freundlichen Grüßen', 'normalStyle');
            $section->addTextBreak(3);

            // Unterschrift
            $section->addText($data['signatory'], 'normalStyle');
            $section->addText('Marktleitung', 'normalStyle');

            // Speichere das Dokument temporär
            $tempFile = tempnam(sys_get_temp_dir(), 'termination_') . '.docx';
            $writer = IOFactory::createWriter($phpWord, 'Word2007');
            $writer->save($tempFile);

            // Erstelle Response mit dem Dokument
            $response = new Response(file_get_contents($tempFile));
            $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            $response->headers->set('Content-Disposition', 'attachment; filename="Kuendigung_' . str_replace(' ', '_', $data['employeeName']) . '.docx"');

            // Lösche temporäre Datei
            unlink($tempFile);

            return $response;

        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Fehler beim Generieren: ' . $e->getMessage()], 500);
        }
    }

    private function getTerminationTypeText(string $type): string
    {
        return match($type) {
            'fristlos' => 'Fristlose Kündigung',
            'probezeit' => 'Kündigung während der Probezeit',
            'fristgerecht' => 'Ordentliche Kündigung',
            default => 'Kündigung'
        };
    }

    private function getMarketAddress(array $data): string
    {
        // Bestimme Marktadresse basierend auf Marktnummer oder anderen Daten
        $marketNumber = $data['marketNumber'] ?? $data['employeeMarketNumber'] ?? '';

        if (str_contains($marketNumber, '5877') || str_contains($marketNumber, '877')) {
            return "Markt Bickendorf\nBergheimer Str. 1\n50171 Kerpen-Bickendorf";
        } elseif (str_contains($marketNumber, '5461') || str_contains($marketNumber, '461')) {
            return "Markt Heimerzheim\nSwisttalstr. 1\n53913 Swisttal-Heimerzheim";
        } elseif (str_contains($marketNumber, '5671') || str_contains($marketNumber, '671')) {
            return "Markt Frimmersdorf\nBedburger Str. 1\n50226 Frechen-Frimmersdorf";
        } elseif (str_contains($marketNumber, '5512') || str_contains($marketNumber, '512')) {
            return "Markt Kaster\nKölner Str. 1\n50181 Bedburg-Kaster";
        }

        return "REWE Markt GmbH\nFriedrich-Ebert-Str. 86\n50181 Bedburg";
    }

    private function addTerminationText($section, array $data): void
    {
        $terminationDate = new \DateTime($data['terminationDate']);
        $entryDate = !empty($data['entryDate']) ? new \DateTime($data['entryDate']) : null;

        switch ($data['terminationType']) {
            case 'fristlos':
                $section->addText('hiermit kündigen wir Ihr Arbeitsverhältnis fristlos zum ' . $terminationDate->format('d.m.Y') . '.', 'normalStyle');
                $section->addTextBreak();
                $section->addText('Grund für die fristlose Kündigung: [Grund hier einfügen]', 'normalStyle');
                break;

            case 'probezeit':
                $section->addText('hiermit kündigen wir Ihr Arbeitsverhältnis während der Probezeit zum ' . $terminationDate->format('d.m.Y') . '.', 'normalStyle');
                if ($entryDate) {
                    $section->addTextBreak();
                    $section->addText('Ihr Arbeitsverhältnis begann am ' . $entryDate->format('d.m.Y') . '.', 'normalStyle');
                }
                break;

            case 'fristgerecht':
            default:
                $section->addText('hiermit kündigen wir Ihr Arbeitsverhältnis ordentlich zum ' . $terminationDate->format('d.m.Y') . '.', 'normalStyle');
                if ($entryDate) {
                    $section->addTextBreak();
                    $section->addText('Ihr Arbeitsverhältnis begann am ' . $entryDate->format('d.m.Y') . '.', 'normalStyle');
                }
                break;
        }

        $section->addTextBreak();
        $section->addText('Wir bitten Sie, uns bis zum Beendigungstermin alle Unterlagen, Schlüssel und sonstigen Gegenstände, die sich in Ihrem Besitz befinden und unserem Unternehmen gehören, zurückzugeben.', 'normalStyle');
        $section->addTextBreak();
        $section->addText('Ihr Arbeitszeugnis erhalten Sie in den nächsten Tagen.', 'normalStyle');
    }
}

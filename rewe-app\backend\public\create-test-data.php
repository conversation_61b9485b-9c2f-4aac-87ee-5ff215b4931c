<?php
// Erstelle Testdaten für die REWE Personalmanagement-App

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');

// Lade die db.php Datei
require_once 'db.php';

// Erstelle Testdaten
$testEmployees = [
    [
        'id' => 1,
        'personnelNumber' => '12345',
        'name' => '<PERSON>ck<PERSON>, <PERSON>',
        'position' => 'Verkäufer',
        'department' => '10',
        'marketNumber' => '5669475512',
        'entryDate' => '01.01.2020',
        'exitDate' => '',
        'birthdate' => '15.03.1985',
        'address' => 'Friedrich-Ebert-Str. 86',
        'zipCity' => '50181 Bedburg',
        'status' => 'aktiv',
        'active' => true,
        'workArea' => 'Verkauf'
    ],
    [
        'id' => 2,
        'personnelNumber' => '12346',
        'name' => '<PERSON>, <PERSON>',
        'position' => 'Kassiererin',
        'department' => '20',
        'marketNumber' => '5669475512',
        'entryDate' => '15.06.2021',
        'exitDate' => '',
        'birthdate' => '22.07.1990',
        'address' => 'Hauptstraße 123',
        'zipCity' => '50181 Bedburg',
        'status' => 'aktiv',
        'active' => true,
        'workArea' => 'Kasse'
    ],
    [
        'id' => 3,
        'personnelNumber' => '12347',
        'name' => 'Schmidt, Peter',
        'position' => 'Abteilungsleiter',
        'department' => '10',
        'marketNumber' => '5669475512',
        'entryDate' => '01.03.2019',
        'exitDate' => '',
        'birthdate' => '10.12.1980',
        'address' => 'Bergstraße 45',
        'zipCity' => '50181 Bedburg',
        'status' => 'aktiv',
        'active' => true,
        'workArea' => 'Leitung'
    ]
];

// Erstelle Gehaltsdaten für die Testmitarbeiter
$testSalaryData = [];
foreach ($testEmployees as $employee) {
    $personnelNumber = $employee['personnelNumber'];
    
    // Erstelle Gehaltsdaten für die letzten 3 Monate
    for ($i = 0; $i < 3; $i++) {
        $date = new DateTime();
        $date->sub(new DateInterval("P{$i}M"));
        $monthYear = $date->format('m.Y');
        
        $key = $personnelNumber . '_' . $date->format('mY') . '_001';
        
        $testSalaryData[$key] = [
            'PersNr' => $personnelNumber,
            'Mitarbeiter' => $employee['name'],
            'Stelle' => $employee['position'],
            'Abteilung' => $employee['department'],
            'Monat_Jahr' => $monthYear,
            'Kostenstelle' => $employee['marketNumber'],
            'Geburtsdatum' => $employee['birthdate'],
            'Eintrittsdatum(anrechenbar)' => $employee['entryDate'],
            'Austrittsdatum' => $employee['exitDate'],
            'Vertragsart' => 'unbefristet',
            'Tarifgebiet Kuerzel' => 'NRW/W',
            'Tarifgebiet' => 'NRW/W Partner',
            'Tarifgruppe' => 'TG G M',
            'Tarifstufe' => 'A',
            'BS-Kennzeichen' => 'V2',
            'Wochen-Stunden' => '38',
            'Soll-Stunden' => '164.67',
            'Stunden bezahlt' => '164.67',
            'Mehrarbeitsstd. bez.' => '0',
            'Brutto Std-Lohn' => '15.0',
            'vertragliche Bezuege' => '2470',
            'Basisbezuege vertraglich' => '2470',
            'Zulagen Gesamt vertraglich' => '0',
            'Funktionszulagen vertraglich' => '0',
            'freiwillige Zulagen vertraglich' => '0',
            'Mehrarbeitspauschale vertraglich' => '0',
            'Zulage Kasse vertraglich' => '0',
            'sonstige Zulagen vertraglich' => '0',
            'bezahlte Fehlzeiten' => '0',
            'AG-SV-Gesamt' => '500.41',
            'Gesamtkosten/ h' => '18.04',
            'Gesamtkosten' => '2970.41'
        ];
    }
}

// Speichere die Testdaten in der globalen Datenbank
$db['employees'] = $testEmployees;
$db['salary_data'] = $testSalaryData;

// Erstelle auch employee_data
$testEmployeeData = [];
foreach ($testEmployees as $employee) {
    $testEmployeeData[$employee['personnelNumber']] = [
        'PersNr' => $employee['personnelNumber'],
        'Name' => $employee['name'],
        'Geburtsdatum' => $employee['birthdate'],
        'Markt/ Einheit' => $employee['marketNumber'],
        'Abteilung' => $employee['department'],
        'Stelle' => $employee['position'],
        'Eintrittsdatum' => $employee['entryDate'],
        'Austrittsdatum' => $employee['exitDate']
    ];
}
$db['employee_data'] = $testEmployeeData;

// Aktualisiere Import-Status
$currentDate = new DateTime();
$year = $currentDate->format('Y');
$month = $currentDate->format('m');

$db['import_status'][$year][$month] = [
    'employee_data' => true,
    'salary_data' => true
];

// Speichere die Datenbank
saveDatabase();

echo json_encode([
    'success' => true,
    'message' => 'Testdaten erfolgreich erstellt',
    'employees_created' => count($testEmployees),
    'salary_records_created' => count($testSalaryData),
    'employee_data_created' => count($testEmployeeData)
]);
?>

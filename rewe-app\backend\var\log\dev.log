[2025-05-24T14:13:13.302815+00:00] deprecation.INFO: User Deprecated: The "ApiPlatform\Hydra\Serializer\HydraPrefixNameConverter" class implements "Symfony\Component\Serializer\NameConverter\AdvancedNameConverterInterface" that is deprecated since Symfony 7.2, use NameConverterInterface instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"ApiPlatform\\Hydra\\Serializer\\HydraPrefixNameConverter\" class implements \"Symfony\\Component\\Serializer\\NameConverter\\AdvancedNameConverterInterface\" that is deprecated since Symfony 7.2, use NameConverterInterface instead. at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\error-handler\\DebugClassLoader.php:345)"} []
[2025-05-24T15:01:41.255753+00:00] deprecation.INFO: User Deprecated: The "ApiPlatform\Hydra\Serializer\HydraPrefixNameConverter" class implements "Symfony\Component\Serializer\NameConverter\AdvancedNameConverterInterface" that is deprecated since Symfony 7.2, use NameConverterInterface instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"ApiPlatform\\Hydra\\Serializer\\HydraPrefixNameConverter\" class implements \"Symfony\\Component\\Serializer\\NameConverter\\AdvancedNameConverterInterface\" that is deprecated since Symfony 7.2, use NameConverterInterface instead. at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\error-handler\\DebugClassLoader.php:345)"} []
[2025-05-24T15:01:41.575451+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:41.575876+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:49.268284+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:01:53.232067+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:53.241600+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:53.459773+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:01:54.775575+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:54.783328+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:54.981222+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:01:56.142329+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:56.149105+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:56.339991+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:01:57.504908+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:57.513392+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:57.709675+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:01:58.928625+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:58.932473+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:01:59.121012+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:00.336913+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:00.344736+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:00.528578+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:01.725169+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:01.730492+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:01.911783+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:02.842505+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:02.846868+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:02.976565+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:04.454222+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:04.459624+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:04.591332+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:05.743849+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:05.752301+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:05.963547+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:06.931699+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:06.936790+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:07.075912+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:08.098183+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:08.103087+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:08.212695+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:09.155367+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:09.160282+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:10.070066+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:10.073997+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerRCBZCG3\\App_KernelDevDebugContainer.php","line":1990,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:10.378380+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:24.678467+00:00] deprecation.INFO: User Deprecated: The "ApiPlatform\Hydra\Serializer\HydraPrefixNameConverter" class implements "Symfony\Component\Serializer\NameConverter\AdvancedNameConverterInterface" that is deprecated since Symfony 7.2, use NameConverterInterface instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"ApiPlatform\\Hydra\\Serializer\\HydraPrefixNameConverter\" class implements \"Symfony\\Component\\Serializer\\NameConverter\\AdvancedNameConverterInterface\" that is deprecated since Symfony 7.2, use NameConverterInterface instead. at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\error-handler\\DebugClassLoader.php:345)"} []
[2025-05-24T15:02:24.690346+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:24.690650+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:25.130121+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:26.371036+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:26.377729+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:26.550328+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:27.458249+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:27.462014+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:27.597413+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:28.628671+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:28.632946+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:28.757929+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:29.822878+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:29.830459+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:29.974322+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:31.033680+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:31.038980+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:31.171796+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:32.165983+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:32.172662+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:32.336528+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:33.339511+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:33.347994+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:33.521697+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:34.674079+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:34.679253+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:34.837097+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:35.823428+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:35.830056+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:36.023249+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:37.090681+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:37.094904+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:37.224837+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:38.456896+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:38.461417+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:38.586984+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:40.006893+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:40.015174+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:41.237409+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:41.245010+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:41.387744+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:42.920258+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:42.928746+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:43.159855+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:44.629930+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:44.637768+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:44.837072+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:46.357082+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:46.365945+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:46.549176+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:48.020299+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:48.026349+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:48.222086+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:49.662521+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:49.668310+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:49.854693+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:51.389940+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:51.397668+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:51.601600+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:53.073606+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:53.081412+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:53.284989+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:54.687959+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:54.694647+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:54.859620+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:56.256005+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:56.263243+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:56.457025+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:57.829189+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:57.833237+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:58.031427+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:02:59.419107+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:59.427810+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:02:59.639287+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:01.148063+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:01.155438+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:01.352409+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:02.820630+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:02.830270+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:03.030521+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:04.507489+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:04.512841+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:04.703211+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:06.179178+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:06.187776+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:06.405357+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:07.893850+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:07.902191+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:08.102277+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:09.530122+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:09.537900+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:09.707337+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:11.209126+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:11.217408+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:11.435712+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:12.808560+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:12.817354+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:13.059673+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:14.468127+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:14.474673+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:14.663607+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:16.078017+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:16.084885+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:16.234772+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:17.544371+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:17.552719+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:17.745891+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:19.170818+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:19.179576+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:19.383931+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:20.565322+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:20.570324+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:20.752962+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:22.030053+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:22.037375+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:22.204416+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:23.560786+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:23.569155+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:23.769847+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:25.201616+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:25.210241+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:25.418131+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:26.775623+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:26.780521+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:26.924353+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:28.122753+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:28.126973+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:28.247343+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:29.313034+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:29.321031+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:29.491892+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:30.620724+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:30.624480+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:30.754485+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:31.739410+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:31.743598+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:31.872735+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:33.200205+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:33.209075+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:33.426299+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:34.919118+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:34.926414+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:35.121209+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:36.539279+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:36.546030+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:36.749387+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:38.122270+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:38.131798+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:38.345451+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:39.879845+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:39.888377+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:40.087277+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:41.546661+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:41.554484+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:41.750359+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:43.142132+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:43.148680+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:43.351966+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:45.025738+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:45.031016+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:45.237043+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:46.579331+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:46.585160+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:46.794858+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:48.460168+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:48.468391+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:48.659676+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:50.055428+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:50.062449+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:50.262627+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:51.610022+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:51.616809+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:51.746830+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:53.105974+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:53.114007+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:53.311917+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:54.831931+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:54.840350+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:55.051543+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:56.547003+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:56.555126+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:56.760333+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:58.167189+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:58.175740+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:58.375049+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:03:59.886933+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:03:59.890712+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:00.045552+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:01.490956+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:01.498869+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:01.678842+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:02.957629+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:02.966571+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:03.143278+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:04.524345+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:04.532752+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:04.741596+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:06.294604+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:06.302172+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:06.492906+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:07.985803+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:07.994029+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:08.207696+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:09.642731+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:09.650096+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:09.850965+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:11.241062+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:11.248795+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:11.455465+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:12.776559+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:12.785577+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:12.989885+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:14.495916+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:14.504079+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:14.690292+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:16.116913+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:16.124409+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:16.315084+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:17.653604+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:17.660281+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:17.850248+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:19.218383+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:19.223492+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:19.422065+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:20.968698+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:20.975790+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:21.142018+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:22.440400+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:22.448221+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:22.643822+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:24.048410+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:24.055560+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:24.244248+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:25.582721+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:25.589118+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:25.807784+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:27.375801+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:27.383549+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:27.579584+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:28.921398+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:28.928503+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:29.090866+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:30.163707+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:30.170873+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:30.327997+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:31.365882+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:31.372154+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:31.512497+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:32.490251+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:32.495056+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:32.697375+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:33.894627+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:33.903650+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:34.099688+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:35.173598+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:35.177973+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:35.330053+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:36.319461+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:36.323780+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:36.484299+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:37.659275+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:37.663919+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:37.817239+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:39.089174+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:39.097069+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:39.291382+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "POST http://localhost:8000/api/login" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"POST http://localhost:8000/api/login\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/login\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:40.636383+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:40.642890+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:40.822464+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:42.321456+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:42.329645+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:42.499741+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:43.863752+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:43.872459+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:44.094861+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:45.581668+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:45.588645+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:45.773482+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:47.131000+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:47.140071+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:47.340578+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:48.822187+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:48.829654+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:49.014965+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:50.358883+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:50.368218+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:50.595179+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:52.040297+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:52.048184+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:52.242732+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:53.654599+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:53.661963+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:53.857053+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:55.269548+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:55.276778+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:55.416011+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:56.726295+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:56.730923+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:56.947264+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:04:58.482998+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:58.491711+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:04:58.703419+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:00.141581+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:00.149504+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:00.286308+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:01.694604+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:01.700331+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:01.880881+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:03.323951+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:03.331927+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:03.527461+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:04.994880+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:05.002342+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:05.228512+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:06.722941+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:06.730434+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:06.925330+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:08.409113+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:08.418062+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:08.654982+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:10.112159+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:10.119210+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:10.305947+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:11.726170+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:11.735861+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:11.948663+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/employees" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/employees\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/employees/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:13.396015+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:13.400005+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:13.580874+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/departments" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/departments\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/departments/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []
[2025-05-24T15:05:14.936833+00:00] php.DEBUG: User Warning: Configure the "curl.cainfo", "openssl.cafile" or "openssl.capath" php.ini setting to enable the CurlHttpClient {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":512,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":58,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:14.944865+00:00] php.DEBUG: User Notice: Upgrade the curl extension or run "composer require amphp/http-client:^4.2.1" to perform async HTTP operations, including full HTTP/2 support {"exception":{"Symfony\\Component\\ErrorHandler\\Exception\\SilencedErrorContext":{"severity":1024,"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-client\\HttpClient.php","line":65,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\var\\cache\\dev\\ContainerOgi04LG\\App_KernelDevDebugContainer.php","line":1989,"function":"create","class":"Symfony\\Component\\HttpClient\\HttpClient","type":"::"}],"count":1}}} []
[2025-05-24T15:05:15.157847+00:00] request.ERROR: Uncaught PHP Exception Symfony\Component\HttpKernel\Exception\NotFoundHttpException: "No route found for "GET http://localhost:8000/api/market-numbers" (from "http://localhost:5173/")" at RouterListener.php line 149 {"exception":"[object] (Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException(code: 0): No route found for \"GET http://localhost:8000/api/market-numbers\" (from \"http://localhost:5173/\") at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\http-kernel\\EventListener\\RouterListener.php:149)\n[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\ResourceNotFoundException(code: 0): No routes found for \"/api/market-numbers/\". at C:\\Users\\<USER>\\Desktop\\KI Kram\\Personalapp\\rewe-app\\backend\\vendor\\symfony\\routing\\Matcher\\Dumper\\CompiledUrlMatcherTrait.php:70)"} []

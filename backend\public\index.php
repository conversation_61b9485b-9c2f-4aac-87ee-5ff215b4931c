<?php
// CORS Headers - <PERSON><PERSON><PERSON><PERSON> VOR ALLEM ANDEREN STEHEN
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle OPTIONS requests (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Content-Type für JSON-Antworten
header('Content-Type: application/json; charset=utf-8');

// Lade die Datenbank-Funktionen
require_once 'db.php';

$uri = $_SERVER['REQUEST_URI'];

// Debug-Ausgabe
error_log("Request URI: " . $uri);
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST Data: " . file_get_contents('php://input'));
}

// Login-Endpunkt
if ($uri === '/api/login' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $username = $data['username'] ?? '';
    $password = $data['password'] ?? '';

    error_log("Login attempt: Username: $username, Password: $password");

    if ($username === 'admin' && $password === 'admin123') {
        error_log("Login successful");
        echo json_encode([
            'username' => 'admin',
            'roles' => ['ROLE_ADMIN'],
            'token' => 'demo-token'
        ]);
    } else {
        error_log("Login failed");
        http_response_code(401);
        echo json_encode(['message' => 'Ungültige Anmeldedaten']);
    }
    exit;
}

// Mitarbeiter-Endpunkte
if (preg_match('/^\/api\/employees(\?.*)?$/', $uri) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Extrahiere Filter aus Query-Parametern
    $filters = [];
    if (isset($_GET['search'])) $filters['search'] = $_GET['search'];
    if (isset($_GET['department'])) $filters['department'] = $_GET['department'];
    if (isset($_GET['location'])) $filters['location'] = $_GET['location'];
    if (isset($_GET['active'])) $filters['active'] = $_GET['active'];

    $employees = getEmployees($filters);
    echo json_encode($employees);
    exit;
}

// Mitarbeiter-Details-Endpunkt
if (preg_match('/^\/api\/employees\/(\d+)$/', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $id = $matches[1];
    $employee = getEmployeeDetails($id);

    if ($employee) {
        echo json_encode($employee);
    } else {
        http_response_code(404);
        echo json_encode(['message' => 'Mitarbeiter nicht gefunden']);
    }
    exit;
}

// Mitarbeiter-Update-Endpunkt
if (preg_match('/^\/api\/employees\/(\d+)$/', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    $id = $matches[1];
    $data = json_decode(file_get_contents('php://input'), true);

    error_log("Mitarbeiter-Update-Anfrage für ID $id: " . json_encode($data));

    $result = updateEmployee($id, $data);

    if ($result) {
        // Hole die aktualisierten Daten
        $employee = getEmployeeDetails($id);
        echo json_encode($employee);
    } else {
        http_response_code(404);
        echo json_encode(['message' => 'Mitarbeiter nicht gefunden']);
    }
    exit;
}

// Import-Status-Endpunkt
if ($uri === '/api/import-status' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $importStatus = getImportStatus();
    echo json_encode($importStatus);
    exit;
}

// Import-Status-Update-Endpunkt
if ($uri === '/api/import-status' && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    $data = json_decode(file_get_contents('php://input'), true);
    $year = $data['year'] ?? date('Y');
    $month = $data['month'] ?? date('m');
    $type = $data['type'] ?? '';
    $status = $data['status'] ?? false;

    if (empty($type)) {
        http_response_code(400);
        echo json_encode(['message' => 'Fehlende Parameter']);
        exit;
    }

    // Debug-Informationen
    error_log("Import-Status-Update erhalten: Typ=$type, Jahr=$year, Monat=$month, Status=" . ($status ? 'true' : 'false'));

    $result = updateImportStatus($year, $month, $type, $status);

    echo json_encode([
        'message' => 'Import-Status aktualisiert',
        'status' => $result
    ]);
    exit;
}

// Import-Endpunkt für CSV-Daten
if ($uri === '/api/import' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $type = $data['type'] ?? '';
    $csvData = $data['data'] ?? '';
    $year = $data['year'] ?? date('Y');
    $month = $data['month'] ?? date('m');

    if (empty($type) || empty($csvData)) {
        error_log("FEHLER: Fehlende Parameter beim Import. Typ: $type, Datensätze: " . (is_array($csvData) ? count($csvData) : 'keine'));
        http_response_code(400);
        echo json_encode(['message' => 'Fehlende Parameter']);
        exit;
    }

    // Debug-Informationen
    error_log("Import-Anfrage erhalten: Typ=$type, Jahr=$year, Monat=$month, Datensätze=" . count($csvData));

    // Zeige die ersten 3 Datensätze im Log
    if (count($csvData) > 0) {
        error_log("Beispiel-Datensätze:");
        for ($i = 0; $i < min(3, count($csvData)); $i++) {
            error_log("Datensatz $i: " . json_encode($csvData[$i], JSON_UNESCAPED_UNICODE));
        }
    }

    // Speichere den aktuellen Datenbankzustand vor dem Import
    $dbBefore = [
        'employees_count' => count($db['employees']),
        'employee_data_count' => count($db['employee_data']),
        'salary_data_count' => count($db['salary_data'])
    ];
    error_log("Datenbankzustand vor Import: " . json_encode($dbBefore));

    $result = importCSV($type, $csvData, $year, $month);
    $logMessage = "CSV-Import für $type ($month/$year): " . ($result ? "Erfolgreich" : "Fehlgeschlagen") . " (" . count($csvData) . " Datensätze)";

    // Speichere den Datenbankzustand nach dem Import
    $dbAfter = [
        'employees_count' => count($db['employees']),
        'employee_data_count' => count($db['employee_data']),
        'salary_data_count' => count($db['salary_data'])
    ];
    error_log("Datenbankzustand nach Import: " . json_encode($dbAfter));
    error_log("Änderungen: Mitarbeiter: " . ($dbAfter['employees_count'] - $dbBefore['employees_count']) .
              ", Mitarbeiterdaten: " . ($dbAfter['employee_data_count'] - $dbBefore['employee_data_count']) .
              ", Gehaltsdaten: " . ($dbAfter['salary_data_count'] - $dbBefore['salary_data_count']));

    // Aktualisiere den Import-Status explizit
    $statusResult = updateImportStatus($year, $month, $type, $result);
    error_log("Import-Status aktualisiert: " . ($statusResult ? "Erfolgreich" : "Fehlgeschlagen"));

    if ($result) {
        echo json_encode([
            'message' => 'Import erfolgreich',
            'log' => $logMessage,
            'count' => count($csvData),
            'db_changes' => [
                'before' => $dbBefore,
                'after' => $dbAfter
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'message' => 'Import fehlgeschlagen',
            'log' => $logMessage,
            'db_changes' => [
                'before' => $dbBefore,
                'after' => $dbAfter
            ]
        ]);
    }
    exit;
}

// Import-Endpunkt für Datei-Uploads (PDF/Excel)
if ($uri === '/api/import-file' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? '';
    $year = $_POST['year'] ?? date('Y');
    $month = $_POST['month'] ?? date('m');

    if (empty($type) || empty($_FILES['file'])) {
        http_response_code(400);
        echo json_encode(['message' => 'Fehlende Parameter oder Datei']);
        exit;
    }

    $file = $_FILES['file'];
    $fileName = $file['name'];
    $fileTmpPath = $file['tmp_name'];
    $fileType = $file['type'];
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // Log-Nachricht vorbereiten
    $logMessage = "Datei-Import für $type ($month/$year): $fileName";

    // Überprüfe Dateityp
    $allowedTypes = [
        'employee_data' => ['pdf', 'csv'],
        'salary_data' => ['xlsx', 'xls', 'csv']
    ];

    if (!in_array($fileExt, $allowedTypes[$type])) {
        $logMessage .= " - Fehlgeschlagen: Ungültiger Dateityp";
        http_response_code(400);
        echo json_encode([
            'message' => 'Ungültiger Dateityp. Erlaubt sind: ' . implode(', ', $allowedTypes[$type]),
            'log' => $logMessage
        ]);
        exit;
    }

    // Verarbeite die Datei basierend auf dem Typ
    if ($fileExt === 'csv') {
        // CSV direkt verarbeiten
        $csvData = [];
        if (($handle = fopen($fileTmpPath, "r")) !== FALSE) {
            // Versuche zuerst mit Semikolon als Trennzeichen
            $firstLine = fgets($handle);
            rewind($handle);

            // Bestimme das Trennzeichen (Semikolon oder Komma)
            $delimiter = (strpos($firstLine, ';') !== false) ? ';' : ',';
            error_log("CSV-Trennzeichen erkannt: " . $delimiter);

            $headers = fgetcsv($handle, 10000, $delimiter);
            error_log("CSV-Header gefunden: " . implode(", ", $headers));

            while (($data = fgetcsv($handle, 10000, $delimiter)) !== FALSE) {
                // Überprüfe, ob die Anzahl der Spalten übereinstimmt
                if (count($data) === count($headers)) {
                    $row = [];
                    foreach ($headers as $i => $header) {
                        $row[$header] = $data[$i];
                    }
                    $csvData[] = $row;
                } else {
                    error_log("Zeile übersprungen: Spaltenanzahl stimmt nicht überein. Header: " . count($headers) . ", Daten: " . count($data));
                }
            }
            fclose($handle);

            error_log("Insgesamt " . count($csvData) . " Zeilen aus CSV gelesen");
        }

        $result = importCSV($type, $csvData, $year, $month);
        $logMessage .= " - " . ($result ? "Erfolgreich" : "Fehlgeschlagen");

        if ($result) {
            echo json_encode([
                'message' => 'Import erfolgreich',
                'log' => $logMessage,
                'rows' => count($csvData)
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'message' => 'Import fehlgeschlagen',
                'log' => $logMessage
            ]);
        }
    } else if ($fileExt === 'pdf' || $fileExt === 'xlsx' || $fileExt === 'xls') {
        // Speichere die Datei temporär
        $uploadDir = __DIR__ . '/uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $uploadFile = $uploadDir . uniqid() . '.' . $fileExt;
        if (move_uploaded_file($fileTmpPath, $uploadFile)) {
            // Konvertiere PDF/Excel zu CSV mit dem Python-Skript im Hintergrund
            $pythonScript = __DIR__ . '/../../import_csv.py';

            // Starte den Python-Importer im Hintergrund (Windows)
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $command = "start /min /b python \"$pythonScript\" \"$uploadFile\" $type $year $month";
                pclose(popen($command, 'r'));
                $returnCode = 0; // Wir nehmen an, dass der Prozess erfolgreich gestartet wurde
            } else {
                // Für Linux/Unix-Systeme
                $command = "python \"$pythonScript\" \"$uploadFile\" $type $year $month > /dev/null 2>&1 &";
                exec($command, $output, $returnCode);
            }

            // Wir löschen die temporäre Datei nicht, da der Python-Prozess sie noch benötigt
            // Der Python-Prozess sollte die Datei selbst löschen, wenn er fertig ist

            // Da der Python-Importer im Hintergrund läuft, können wir nicht sofort auf die CSV-Datei zugreifen
            // Stattdessen aktualisieren wir den Import-Status direkt

            // Aktualisiere den Import-Status auf "in Bearbeitung"
            updateImportStatus($year, $month, $type, false, 'processing');

            $logMessage .= " - Datei hochgeladen, Verarbeitung läuft im Hintergrund";

            // Sende eine Erfolgsmeldung zurück, dass der Upload erfolgreich war
            echo json_encode([
                'message' => 'Datei erfolgreich hochgeladen. Die Verarbeitung läuft im Hintergrund.',
                'log' => $logMessage,
                'status' => 'processing'
            ]);
        } else {
            $logMessage .= " - Fehlgeschlagen: Datei-Upload fehlgeschlagen";
            http_response_code(500);
            echo json_encode([
                'message' => 'Datei-Upload fehlgeschlagen',
                'log' => $logMessage
            ]);
        }
    }

    exit;
}

// Vorschau-Endpunkt für importierte Daten
if (preg_match('/^\/api\/import-preview\/([^\/]+)\/(\d{4})\/(\d{2})$/', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $type = $matches[1];
    $year = $matches[2];
    $month = $matches[3];

    $data = getImportedData($year, $month, $type);

    if ($data) {
        echo json_encode($data);
    } else {
        http_response_code(404);
        echo json_encode(['message' => 'Keine Daten gefunden']);
    }
    exit;
}

// Update Import-Status-Endpunkt
if ($uri === '/api/import-status' && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    $data = json_decode(file_get_contents('php://input'), true);
    $year = $data['year'] ?? date('Y');
    $month = $data['month'] ?? date('m');
    $type = $data['type'] ?? '';
    $status = $data['status'] ?? false;

    if (empty($type)) {
        http_response_code(400);
        echo json_encode(['message' => 'Fehlende Parameter']);
        exit;
    }

    $result = updateImportStatus($year, $month, $type, $status);
    echo json_encode($result);
    exit;
}

// Abteilungen-Endpunkt (für Filter)
if ($uri === '/api/departments' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // In einer echten Anwendung würden diese aus der Datenbank kommen
    echo json_encode([
        'Verkauf',
        'Kasse',
        'Marketing',
        'IT',
        'Logistik',
        'Management'
    ]);
    exit;
}

// Marktnummern-Endpunkt (für Filter)
if ($uri === '/api/market-numbers' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // In einer echten Anwendung würden diese aus der Datenbank kommen
    // Formatierte Marktnummern mit Namen, damit sie mit der Anzeige übereinstimmen
    echo json_encode([
        '5671 - Frimmersdorf',
        '5877 - Bickendorf',
        '5512 - Kaster',
        '5461 - Heimerzheim'
    ]);
    exit;
}

// Import-Löschen-Endpunkt
if (preg_match('/^\/api\/delete-import\/([^\/]+)\/(\d{4})\/(\d{2})$/', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $type = $matches[1];
    $year = $matches[2];
    $month = $matches[3];

    $result = deleteImport($type, $year, $month);
    $logMessage = "Löschvorgang für $type ($month/$year): " . ($result ? "Erfolgreich" : "Fehlgeschlagen");

    if ($result) {
        echo json_encode([
            'message' => 'Import erfolgreich gelöscht',
            'log' => $logMessage
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'message' => 'Löschen fehlgeschlagen',
            'log' => $logMessage
        ]);
    }
    exit;
}

// Kündigungsgenerator-Endpunkt
if ($uri === '/api/termination/generate' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'termination.php';
    exit;
}

// Dashboard-Endpunkt
if ($uri === '/api/dashboard' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $dashboardData = getDashboardData();
    echo json_encode($dashboardData);
    exit;
}

// Datenbank-Endpunkt (für Debug-Zwecke)
if ($uri === '/api/database' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Erstelle eine sichere Kopie der Datenbank ohne sensible Daten
    $safeDb = [
        'employees_count' => count($db['employees']),
        'employee_data_count' => count($db['employee_data']),
        'salary_data_count' => count($db['salary_data']),
        'import_status' => $db['import_status'],
        'employees' => array_slice($db['employees'], 0, 10), // Nur die ersten 10 Mitarbeiter
        'employee_data_sample' => array_slice($db['employee_data'], 0, 5), // Nur die ersten 5 Mitarbeiterdaten
        'salary_data_sample' => array_slice($db['salary_data'], 0, 5), // Nur die ersten 5 Gehaltsdaten
        'data_file_path' => __DIR__ . '/data.json',
        'data_file_exists' => file_exists(__DIR__ . '/data.json'),
        'data_file_size' => file_exists(__DIR__ . '/data.json') ? filesize(__DIR__ . '/data.json') : 0,
        'data_file_modified' => file_exists(__DIR__ . '/data.json') ? date("Y-m-d H:i:s", filemtime(__DIR__ . '/data.json')) : 'N/A',
        'uploads_dir' => __DIR__ . '/uploads',
        'uploads_dir_exists' => is_dir(__DIR__ . '/uploads'),
        'uploads_files' => is_dir(__DIR__ . '/uploads') ? scandir(__DIR__ . '/uploads') : []
    ];

    echo json_encode($safeDb);
    exit;
}

// Fallback für unbekannte Endpunkte
http_response_code(404);
echo json_encode(['message' => 'Endpunkt nicht gefunden']);

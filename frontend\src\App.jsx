import React, { useState, useEffect } from 'react'
import StaffingApp from './StaffingApp'
// Konstante für die Aktualisierung der Filter
const FILTER_UPDATE_INTERVAL = 5000; // 5 Sekunden

function App() {
  // Hauptzustand
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(null);
  const [chatOpen, setChatOpen] = useState(false);

  // Daten
  const [employees, setEmployees] = useState([]);
  const [salaryData, setSalaryData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Chat
  const [messages, setMessages] = useState([
    { id: 1, sender: 'system', text: 'Willkommen im REWE Chat! Wie kann ich Ihnen helfen?', timestamp: new Date() }
  ]);
  const [newMessage, setNewMessage] = useState('');

  // Login
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loginError, setLoginError] = useState('');

  // Lade Mitarbeiterdaten, wenn der Tab wechselt
  useEffect(() => {
    if (activeTab === 'employees' || activeTab === 'employee-detail') {
      fetchEmployees();
    }
  }, [activeTab]);

  // Simuliere Gehaltsdaten (in einer echten App würden diese vom Server kommen)
  useEffect(() => {
    // Simulierte Gehaltsdaten für die Mitarbeiter
    const mockSalaryData = {
      1: {
        current: '3500 €',
        history: [
          { date: '01.01.2025', amount: '3500 €' },
          { date: '01.07.2024', amount: '3400 €' },
          { date: '01.01.2024', amount: '3300 €' }
        ]
      },
      2: {
        current: '4200 €',
        history: [
          { date: '01.01.2025', amount: '4200 €' },
          { date: '01.07.2024', amount: '4000 €' },
          { date: '01.01.2024', amount: '3800 €' }
        ]
      },
      3: {
        current: '5100 €',
        history: [
          { date: '01.01.2025', amount: '5100 €' },
          { date: '01.07.2024', amount: '4900 €' },
          { date: '01.01.2024', amount: '4700 €' }
        ]
      }
    };

    setSalaryData(mockSalaryData);
  }, []);

  // API-Aufrufe
  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/employees');
      const data = await response.json();
      setEmployees(data);
      setError(null);
    } catch (err) {
      setError('Fehler beim Laden der Mitarbeiterdaten');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginError('');

    try {
      const response = await fetch('http://localhost:8000/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (response.ok) {
        const data = await response.json();
        setIsLoggedIn(true);
        setActiveTab('dashboard');
      } else {
        setLoginError('Ungültige Anmeldedaten');
      }
    } catch (err) {
      setLoginError('Fehler bei der Anmeldung. Bitte versuchen Sie es später erneut.');
      console.error(err);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUsername('');
    setPassword('');
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      sender: 'user',
      text: newMessage,
      timestamp: new Date()
    };

    setMessages([...messages, userMessage]);
    setNewMessage('');

    // Simuliere eine Antwort vom System nach 1 Sekunde
    setTimeout(() => {
      const systemResponse = {
        id: messages.length + 2,
        sender: 'system',
        text: `Ich habe Ihre Nachricht "${newMessage}" erhalten. Wie kann ich Ihnen weiterhelfen?`,
        timestamp: new Date()
      };
      setMessages(prevMessages => [...prevMessages, systemResponse]);
    }, 1000);
  };

  // UI-Komponenten
  const renderNavigation = () => {
    return React.createElement('nav', { className: 'navigation' },
      React.createElement('ul', null,
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('dashboard'),
              className: activeTab === 'dashboard' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-home' }),
            'Dashboard'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => {
                setActiveTab('employees');
                setSelectedEmployeeId(null);
              },
              className: activeTab === 'employees' || activeTab === 'employee-detail' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-users' }),
            'Mitarbeiter'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('skills'),
              className: activeTab === 'skills' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-graduation-cap' }),
            'Skilltrees'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('todos'),
              className: activeTab === 'todos' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-tasks' }),
            'To-Dos'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('calendar'),
              className: activeTab === 'calendar' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-calendar-alt' }),
            'Kalender'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('staffing'),
              className: activeTab === 'staffing' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-user-clock' }),
            'Personalplanung'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setChatOpen(!chatOpen),
              className: chatOpen ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-comments' }),
            'Chat'
          )
        ),
        // Admin-Bereich nur für Admin-Benutzer anzeigen
        React.createElement('li', { className: 'admin-section' },
          React.createElement('div', { className: 'nav-section-title' }, 'Admin-Bereich')
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('import'),
              className: activeTab === 'import' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-file-import' }),
            'Daten-Import'
          )
        ),
        React.createElement('li', null,
          React.createElement('button',
            {
              onClick: () => setActiveTab('termination'),
              className: activeTab === 'termination' ? 'active' : ''
            },
            React.createElement('i', { className: 'fas fa-file-contract' }),
            'Kündigungsgenerator'
          )
        ),
        React.createElement('li', { className: 'logout-item' },
          React.createElement('button',
            {
              onClick: handleLogout,
              className: 'logout-button'
            },
            React.createElement('i', { className: 'fas fa-sign-out-alt' }),
            'Abmelden'
          )
        )
      )
    );
  };

  // State für Dokumente - Jetzt mit Unterstützung für mehrere Dokumente pro Typ
  const [employeeDocuments, setEmployeeDocuments] = useState({});

  // State für aktuell ausgewähltes Dokument in der Vorschau
  const [selectedDocumentIndex, setSelectedDocumentIndex] = useState(0);

  // State für Dashboard-Statistiken
  const [dashboardStats, setDashboardStats] = useState({
    activeEmployees: 0,
    openTodos: 12,
    upcomingEvents: 5,
    missingDocuments: 0,
    missingHealthCertificates: 0
  });

  // State für Dokument-Upload
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);
  const [currentDocumentType, setCurrentDocumentType] = useState('');
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [documentPreview, setDocumentPreview] = useState(null);

  // Lade Dashboard-Statistiken
  useEffect(() => {
    if (activeTab === 'dashboard') {
      fetchDashboardStats();
    }
  }, [activeTab, employeeDocuments]);

  const fetchDashboardStats = async () => {
    try {
      // Lade alle Mitarbeiterdaten für die Statistik
      const response = await fetch('http://localhost:8000/api/employees');
      if (response.ok) {
        const data = await response.json();

        // Zähle aktive Mitarbeiter (ohne Austrittsdatum)
        const activeEmployees = data.filter(emp =>
          !emp.exitDate &&
          !(emp.salary_data && emp.salary_data.Austrittsdatum)
        );

        // Zähle fehlende Dokumente
        let missingDocsCount = 0;
        let missingHealthCertificatesCount = 0;

        data.forEach(emp => {
          const docs = employeeDocuments[emp.id] || {};
          if (!docs.candidateSheet || docs.candidateSheet.length === 0) missingDocsCount++;
          if (!docs.workContract || docs.workContract.length === 0) missingDocsCount++;
          if (!docs.inductionPlan || docs.inductionPlan.length === 0) missingDocsCount++;
          if (!docs.probationAssessment || docs.probationAssessment.length === 0) missingDocsCount++;
          if (!docs.annualReview || docs.annualReview.length === 0) missingDocsCount++;

          // Gesundheitszeugnisse separat zählen
          if (!docs.healthCertificate || docs.healthCertificate.length === 0) {
            missingDocsCount++;
            missingHealthCertificatesCount++;
          }
        });

        setDashboardStats(prev => ({
          ...prev,
          activeEmployees: activeEmployees.length,
          missingDocuments: missingDocsCount,
          missingHealthCertificates: missingHealthCertificatesCount
        }));
      }
    } catch (err) {
      console.error('Fehler beim Laden der Dashboard-Statistiken:', err);
    }
  };

  const renderDashboard = () => {
    return React.createElement('div', { className: 'dashboard' },
      React.createElement('div', { className: 'dashboard-header' },
        React.createElement('h2', null, 'Dashboard'),
        React.createElement('p', null, 'Willkommen im REWE Personalmanagement-System.')
      ),

      React.createElement('div', { className: 'dashboard-stats' },
        React.createElement('div', { className: 'stat-card' },
          React.createElement('h3', null, 'Mitarbeiter'),
          React.createElement('div', { className: 'stat-value' }, dashboardStats.activeEmployees.toString()),
          React.createElement('div', { className: 'stat-description' }, 'Aktive Mitarbeiter')
        ),
        React.createElement('div', { className: 'stat-card' },
          React.createElement('h3', null, 'Offene To-Dos'),
          React.createElement('div', { className: 'stat-value' }, dashboardStats.openTodos.toString()),
          React.createElement('div', { className: 'stat-description' }, 'Aufgaben zu erledigen')
        ),
        React.createElement('div', { className: 'stat-card' },
          React.createElement('h3', null, 'Termine'),
          React.createElement('div', { className: 'stat-value' }, dashboardStats.upcomingEvents.toString()),
          React.createElement('div', { className: 'stat-description' }, 'Anstehende Termine')
        ),
        React.createElement('div', { className: 'stat-card warning-card' },
          React.createElement('h3', null, 'Dokumente'),
          React.createElement('div', { className: 'stat-value' }, dashboardStats.missingDocuments.toString()),
          React.createElement('div', { className: 'stat-description' }, 'Fehlende Dokumente')
        ),
        React.createElement('div', { className: 'stat-card warning-card health-certificate-card' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-medkit' }),
            ' Gesundheitszeugnisse'
          ),
          React.createElement('div', { className: 'stat-value' }, dashboardStats.missingHealthCertificates.toString()),
          React.createElement('div', { className: 'stat-description' }, 'Fehlende Gesundheitszeugnisse')
        )
      ),

      React.createElement('h3', null, 'Schnellzugriff'),
      React.createElement('div', { className: 'dashboard-cards' },
        React.createElement('div', { className: 'card' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-users' }),
            'Mitarbeiter'
          ),
          React.createElement('p', null, 'Verwalten Sie Ihre Mitarbeiter und deren Daten.'),
          React.createElement('button', {
            onClick: () => setActiveTab('employees')
          },
            'Anzeigen',
            React.createElement('i', { className: 'fas fa-arrow-right' })
          )
        ),
        React.createElement('div', { className: 'card' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-graduation-cap' }),
            'Skilltrees'
          ),
          React.createElement('p', null, 'Verwalten Sie Mitarbeiterfähigkeiten und Entwicklungspfade.'),
          React.createElement('button', {
            onClick: () => setActiveTab('skills')
          },
            'Anzeigen',
            React.createElement('i', { className: 'fas fa-arrow-right' })
          )
        ),
        React.createElement('div', { className: 'card' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-tasks' }),
            'To-Dos'
          ),
          React.createElement('p', null, 'Verwalten Sie Aufgaben und Projekte.'),
          React.createElement('button', {
            onClick: () => setActiveTab('todos')
          },
            'Anzeigen',
            React.createElement('i', { className: 'fas fa-arrow-right' })
          )
        )
      )
    );
  };

  // State für Mitarbeiter-Filter
  const [searchTerm, setSearchTerm] = useState('');
  const [searchTermDebounced, setSearchTermDebounced] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [marketNumberFilter, setMarketNumberFilter] = useState('');
  const [workAreaFilter, setWorkAreaFilter] = useState('');
  const [activeFilter, setActiveFilter] = useState('');
  const [departments, setDepartments] = useState([]);
  const [marketNumbers, setMarketNumbers] = useState([]);
  const [workAreas, setWorkAreas] = useState([]);

  // Lade Abteilungen, Marktnummern und Einsatzbereiche für Filter
  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        const deptResponse = await fetch('http://localhost:8000/api/departments');
        const marketResponse = await fetch('http://localhost:8000/api/market-numbers');
        const employeesResponse = await fetch('http://localhost:8000/api/employees');

        if (deptResponse.ok && marketResponse.ok && employeesResponse.ok) {
          const deptData = await deptResponse.json();
          const marketData = await marketResponse.json();
          const employeesData = await employeesResponse.json();

          setDepartments(deptData);

          // Formatiere die Marktnummern mit Namen für die Anzeige im Filter
          const formattedMarketNumbers = marketData.map(market => {
            // Formatiere die Marktnummer mit dem Namen
            if (market.includes('877')) {
              return '5877 - Bickendorf';
            } else if (market.includes('461')) {
              return '5461 - Heimerzheim';
            } else if (market.includes('671')) {
              return '5671 - Frimmersdorf';
            } else if (market.includes('512')) {
              return '5512 - Kaster';
            } else {
              // Stelle sicher, dass die Marktnummer mit "5" beginnt
              if (!market.startsWith('5')) {
                return '5' + market;
              }
              return market;
            }
          });

          setMarketNumbers(formattedMarketNumbers);

          // Sammle alle eindeutigen Einsatzbereiche
          // Nur tatsächlich vorhandene Werte verwenden (nicht leere Strings)
          const uniqueWorkAreas = Array.from(new Set(
            employeesData
              .filter(emp => emp.workArea && emp.workArea.trim() !== '')
              .map(emp => emp.workArea)
          )).sort();

          // Setze die Einsatzbereiche für den Filter
          setWorkAreas(uniqueWorkAreas);

          // Wenn der aktuelle Filter nicht mehr in den Daten vorhanden ist, zurücksetzen
          if (workAreaFilter && !uniqueWorkAreas.includes(workAreaFilter)) {
            setWorkAreaFilter('');
          }
        }
      } catch (err) {
        console.error('Fehler beim Laden der Filterdaten:', err);
      }
    };

    // Initial laden
    fetchFilterData();

    // Regelmäßig aktualisieren, um sicherzustellen, dass die Filter immer aktuell sind
    const intervalId = setInterval(fetchFilterData, FILTER_UPDATE_INTERVAL);

    // Aufräumen beim Unmount
    return () => clearInterval(intervalId);
  }, [workAreaFilter]);

  // Lade Mitarbeiterdaten mit Filtern
  const fetchEmployeesWithFilters = async () => {
    setLoading(true);
    try {
      // Zuerst alle Mitarbeiter laden
      const response = await fetch('http://localhost:8000/api/employees');

      if (response.ok) {
        let data = await response.json();
        let filteredData = [...data];

        // Clientseitige Filterung anwenden
        // Suchbegriff-Filter
        if (searchTermDebounced) {
          const searchLower = searchTermDebounced.toLowerCase();
          filteredData = filteredData.filter(employee => {
            return (
              (employee.name && employee.name.toLowerCase().includes(searchLower)) ||
              (employee.department && employee.department.toLowerCase().includes(searchLower)) ||
              (employee.personnelNumber && employee.personnelNumber.toLowerCase().includes(searchLower))
            );
          });
        }

        // Abteilungs-Filter
        if (departmentFilter) {
          filteredData = filteredData.filter(employee =>
            employee.department === departmentFilter
          );
        }

        // Markt-Filter
        if (marketNumberFilter) {
          filteredData = filteredData.filter(employee => {
            // Verwende die gleiche Funktion, die auch in der Tabelle verwendet wird,
            // um die formatierte Marktnummer zu ermitteln
            const getFormattedMarketNumber = () => {
              // Prüfen, ob ein Eintrittsdatum vorhanden ist
              const hasEntryDate = employee.entryDate ||
                (employee.salary_data && employee.salary_data['Eintrittsdatum(anrechenbar)']);

              // Spezielle Logik für Mitarbeiter ohne Eintrittsdatum
              if (!hasEntryDate) {
                // Prüfen, ob Marktnummer vorhanden ist und entsprechend formatieren
                let marketNumber = '';

                if (employee.salary_data && employee.salary_data.Kostenstelle) {
                  marketNumber = employee.salary_data.Kostenstelle.toString().slice(-4);
                } else if (employee.employee_data && employee.employee_data['Markt/ Einheit']) {
                  marketNumber = employee.employee_data['Markt/ Einheit'].toString();
                } else if (employee.marketNumber) {
                  marketNumber = employee.marketNumber.toString();
                }

                // Spezielle Formatierung für bestimmte Marktnummern
                if (marketNumber.includes('877')) {
                  return '5877 - Bickendorf';
                } else if (marketNumber.includes('461')) {
                  return '5461 - Heimerzheim';
                } else if (marketNumber.includes('671')) {
                  return '5671 - Frimmersdorf';
                } else if (marketNumber.includes('512')) {
                  return '5512 - Kaster';
                } else if (marketNumber) {
                  // Wenn die Marktnummer nicht mit "5" beginnt, füge sie hinzu
                  if (!marketNumber.startsWith('5')) {
                    marketNumber = '5' + marketNumber;
                  }
                  return marketNumber;
                }
                return 'Nicht angegeben';
              }

              // Normale Logik für Mitarbeiter mit Eintrittsdatum
              // Für Alina Klassen (1317164) verwenden wir immer 5461
              if (employee.personnelNumber === '1317164') {
                return '5461 - Heimerzheim';
              } else if (employee.salary_data && employee.salary_data.Kostenstelle) {
                let marketNumber = employee.salary_data.Kostenstelle.toString().slice(-4);

                // Spezielle Formatierung für bestimmte Marktnummern
                if (marketNumber.includes('877')) {
                  return '5877 - Bickendorf';
                } else if (marketNumber.includes('461')) {
                  return '5461 - Heimerzheim';
                } else if (marketNumber.includes('671')) {
                  return '5671 - Frimmersdorf';
                } else if (marketNumber.includes('512')) {
                  return '5512 - Kaster';
                } else {
                  return '5' + marketNumber;
                }
              } else if (employee.employee_data && employee.employee_data['Markt/ Einheit']) {
                let marketNumber = employee.employee_data['Markt/ Einheit'].toString();

                // Wenn die Marktnummer nicht mit "5" beginnt, füge sie hinzu
                if (!marketNumber.startsWith('5')) {
                  marketNumber = '5' + marketNumber;
                }

                // Spezielle Formatierung für bestimmte Marktnummern
                if (marketNumber.includes('5877')) {
                  return '5877 - Bickendorf';
                } else if (marketNumber.includes('5461')) {
                  return '5461 - Heimerzheim';
                } else if (marketNumber.includes('5671')) {
                  return '5671 - Frimmersdorf';
                } else if (marketNumber.includes('5512')) {
                  return '5512 - Kaster';
                } else {
                  return marketNumber;
                }
              } else if (employee.marketNumber) {
                let marketNumber = employee.marketNumber.toString();

                // Wenn die Marktnummer nicht mit "5" beginnt, füge sie hinzu
                if (!marketNumber.startsWith('5')) {
                  marketNumber = '5' + marketNumber;
                }

                // Spezielle Formatierung für bestimmte Marktnummern
                if (marketNumber.includes('5877')) {
                  return '5877 - Bickendorf';
                } else if (marketNumber.includes('5461')) {
                  return '5461 - Heimerzheim';
                } else if (marketNumber.includes('5671')) {
                  return '5671 - Frimmersdorf';
                } else if (marketNumber.includes('5512')) {
                  return '5512 - Kaster';
                } else {
                  return marketNumber;
                }
              }

              return 'Nicht angegeben';
            };

            // Hole die formatierte Marktnummer für diesen Mitarbeiter
            const formattedMarket = getFormattedMarketNumber();

            // Vergleiche mit dem ausgewählten Filter
            return formattedMarket === marketNumberFilter;
          });
        }

        // Einsatzbereich-Filter
        if (workAreaFilter) {
          filteredData = filteredData.filter(employee =>
            employee.workArea === workAreaFilter
          );
        }

        // Status-Filter
        if (activeFilter) {
          filteredData = filterEmployeesByStatus(filteredData, activeFilter);
        }

        // Sortiere die Mitarbeiter alphabetisch nach Namen
        filteredData.sort((a, b) => {
          if (a.name && b.name) {
            return a.name.localeCompare(b.name, 'de', { sensitivity: 'base' });
          }
          return 0;
        });

        setEmployees(filteredData);
        setError(null);
      } else {
        setError('Fehler beim Laden der Mitarbeiterdaten');
      }
    } catch (err) {
      setError('Fehler beim Laden der Mitarbeiterdaten');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Verzögere die Suche, um zu verhindern, dass das Suchfeld den Fokus verliert
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchTermDebounced(searchTerm);
    }, 800); // 800ms Verzögerung für bessere Benutzererfahrung

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Aktualisiere Mitarbeiterdaten, wenn Filter geändert werden
  // Verwende useRef, um zu verhindern, dass die Seite bei jedem Tastendruck neu geladen wird
  const isInitialMount = React.useRef(true);

  useEffect(() => {
    // Überspringe den ersten Aufruf beim Mounten der Komponente
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Nur wenn der Tab "employees" aktiv ist
    if (activeTab === 'employees') {
      fetchEmployeesWithFilters();
    }
  }, [activeTab, searchTermDebounced, departmentFilter, marketNumberFilter, workAreaFilter, activeFilter]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Filtere Mitarbeiter basierend auf dem Status
  const filterEmployeesByStatus = (employees, statusFilter) => {
    if (!statusFilter) return employees;

    return employees.filter(employee => {
      // Bestimme den Status des Mitarbeiters
      let employeeStatus = 'Aktiv'; // Standardwert

      // Wenn kein Eintrittsdatum vorhanden ist, Status = Inaktiv
      if (!employee.entryDate &&
          !(employee.salary_data && employee.salary_data['Eintrittsdatum(anrechenbar)'])) {
        employeeStatus = 'Inaktiv';
      }

      // Wenn Austrittsdatum vorhanden ist, Status = Austritt
      if (employee.exitDate || (employee.salary_data && employee.salary_data.Austrittsdatum)) {
        employeeStatus = 'Austritt';
      }

      // Wenn manuell gesetzter Status vorhanden ist, diesen verwenden
      if (employee.status) {
        employeeStatus = employee.status;
      }

      // Vergleiche den Status mit dem Filter
      return employeeStatus === statusFilter;
    });
  };

  const handleFilterChange = (filter, value) => {
    switch (filter) {
      case 'department':
        setDepartmentFilter(value);
        break;
      case 'marketNumber':
        setMarketNumberFilter(value);
        break;
      case 'workArea':
        setWorkAreaFilter(value);
        break;
      case 'active':
        setActiveFilter(value);
        break;
      default:
        break;
    }
  };

  const renderEmployees = () => {
    if (loading) {
      return React.createElement('div', { className: 'loading' },
        React.createElement('i', { className: 'fas fa-spinner' }),
        'Lade Mitarbeiterdaten...'
      );
    }

    if (error) {
      return React.createElement('div', { className: 'error' }, error);
    }

    return React.createElement('div', { className: 'employees' },
      // Header mit Suchfeld
      React.createElement('div', { className: 'employees-header' },
        React.createElement('h2', null, 'Mitarbeiterliste'),
        React.createElement('div', { className: 'search-bar' },
          React.createElement('i', { className: 'fas fa-search' }),
          React.createElement('input', {
            type: 'text',
            placeholder: 'Mitarbeiter suchen...',
            value: searchTerm,
            onChange: handleSearchChange
          })
        )
      ),

      // Filter-Bereich
      React.createElement('div', { className: 'filter-container' },
        // Abteilungsfilter
        React.createElement('div', { className: 'filter-group' },
          React.createElement('label', null, 'Abteilung:'),
          React.createElement('select', {
            value: departmentFilter,
            onChange: (e) => handleFilterChange('department', e.target.value)
          },
            React.createElement('option', { value: '' }, 'Alle'),
            // Sammle alle eindeutigen Abteilungen aus allen Mitarbeitern
            (() => {
              // Hole alle Mitarbeiter vom Server, um die Filter zu füllen
              const allDepartments = Array.from(new Set(employees
                .filter(emp => emp.department && emp.department !== 'Nicht angegeben')
                .map(emp => emp.department)))
                .sort();

              return allDepartments.map(dept =>
                React.createElement('option', { key: dept, value: dept }, dept)
              );
            })()
          )
        ),

        // Marktfilter
        React.createElement('div', { className: 'filter-group' },
          React.createElement('label', null, 'Markt:'),
          React.createElement('select', {
            value: marketNumberFilter,
            onChange: (e) => handleFilterChange('marketNumber', e.target.value)
          },
            React.createElement('option', { value: '' }, 'Alle'),
            // Verwende die bereits formatierten Marktnummern
            (() => {
              const filteredMarkets = marketNumbers
                .filter(market => market !== '5329'); // Entferne den Wert 5329

              return filteredMarkets.map(market => {
                // Verwende den formatierten Marktnamen direkt als Wert und Anzeige
                return React.createElement('option', { key: market, value: market }, market);
              });
            })()
          )
        ),

        // Einsatzbereich-Filter
        React.createElement('div', { className: 'filter-group' },
          React.createElement('label', null, 'Einsatzbereich:'),
          React.createElement('select', {
            value: workAreaFilter,
            onChange: (e) => handleFilterChange('workArea', e.target.value)
          },
            React.createElement('option', { value: '' }, 'Alle'),
            // Verwende nur die tatsächlich vorhandenen Einsatzbereiche
            workAreas && workAreas.length > 0
              ? workAreas.map(area =>
                  React.createElement('option', { key: area, value: area }, area)
                )
              : null
          )
        ),

        // Statusfilter
        React.createElement('div', { className: 'filter-group' },
          React.createElement('label', null, 'Status:'),
          React.createElement('select', {
            value: activeFilter,
            onChange: (e) => handleFilterChange('active', e.target.value)
          },
            React.createElement('option', { value: '' }, 'Alle'),
            React.createElement('option', { value: 'Aktiv' }, 'Aktiv'),
            React.createElement('option', { value: 'Austritt' }, 'Austritt')
          )
        ),

        React.createElement('button', {
          className: 'filter-reset-button',
          onClick: () => {
            setSearchTerm('');
            setDepartmentFilter('');
            setMarketNumberFilter('');
            setWorkAreaFilter('');
            setActiveFilter('');
          }
        }, 'Filter zurücksetzen')
      ),

      // Mitarbeitertabelle
      React.createElement('div', { className: 'employee-table-container' },
        React.createElement('table', { className: 'employee-table' },
          React.createElement('thead', null,
            React.createElement('tr', null,
              React.createElement('th', null, 'Name'),
              React.createElement('th', null, 'Abteilung'),
              React.createElement('th', null, 'Einsatzbereich'),
              React.createElement('th', null, 'Markt'),
              React.createElement('th', null, 'Fehlende Dokumente'),
              React.createElement('th', null, 'Status'),
              React.createElement('th', null, 'Aktionen')
            )
          ),
          React.createElement('tbody', null,
            employees.length === 0 ?
              React.createElement('tr', null,
                React.createElement('td', { colSpan: 7, className: 'no-data' }, 'Keine Mitarbeiter gefunden')
              ) :
              employees.map(employee =>
                React.createElement('tr', { key: employee.id },
                  React.createElement('td', null, employee.name),
                  React.createElement('td', null, employee.department),
                  React.createElement('td', null, employee.workArea || 'Nicht angegeben'),
                  React.createElement('td', null,
                    (() => {
                      // Prüfen, ob ein Eintrittsdatum vorhanden ist
                      const hasEntryDate = employee.entryDate ||
                        (employee.salary_data && employee.salary_data['Eintrittsdatum(anrechenbar)']);

                      // Spezielle Logik für Mitarbeiter ohne Eintrittsdatum
                      if (!hasEntryDate) {
                        // Prüfen, ob Marktnummer vorhanden ist und entsprechend formatieren
                        let marketNumber = '';

                        if (employee.salary_data && employee.salary_data.Kostenstelle) {
                          marketNumber = employee.salary_data.Kostenstelle.toString().slice(-4);
                        } else if (employee.employee_data && employee.employee_data['Markt/ Einheit']) {
                          marketNumber = employee.employee_data['Markt/ Einheit'].toString();
                        } else if (employee.marketNumber) {
                          marketNumber = employee.marketNumber.toString();
                        }

                        // Spezielle Formatierung für bestimmte Marktnummern
                        if (marketNumber.includes('877')) {
                          return '5877 - Bickendorf';
                        } else if (marketNumber.includes('461')) {
                          return '5461 - Heimerzheim';
                        } else if (marketNumber.includes('671')) {
                          return '5671 - Frimmersdorf';
                        } else if (marketNumber.includes('512')) {
                          return '5512 - Kaster';
                        } else if (marketNumber) {
                          return formatMarketNumber(marketNumber, true);
                        } else {
                          return 'Nicht angegeben';
                        }
                      }

                      // Verwende die Marktnummer aus den Gehaltsdaten mit vertraglichen Bezügen
                      // Für Alina Klassen (1317164) verwenden wir immer 5461
                      if (employee.personnelNumber === '1317164') {
                        return '5461 - Heimerzheim';
                      }

                      // Für andere Mitarbeiter: Normale Logik
                      if (employee.salary_data && employee.salary_data.Kostenstelle) {
                        return formatMarketNumber(employee.salary_data.Kostenstelle.toString().slice(-4), false);
                      } else if (employee.employee_data && employee.employee_data['Markt/ Einheit']) {
                        return formatMarketNumber(employee.employee_data['Markt/ Einheit'], true);
                      } else if (employee.marketNumber) {
                        return formatMarketNumber(employee.marketNumber, true);
                      } else {
                        return 'Nicht angegeben';
                      }
                    })()
                  ),
                  React.createElement('td', null,
                    (() => {
                      // Prüfen, ob Dokumente fehlen
                      const docs = employeeDocuments[employee.id] || {};
                      const missingDocs = [];

                      if (!docs.candidateSheet || docs.candidateSheet.length === 0) missingDocs.push('Kandidatenstammblatt');
                      if (!docs.workContract || docs.workContract.length === 0) missingDocs.push('Arbeitsvertrag');
                      if (!docs.inductionPlan || docs.inductionPlan.length === 0) missingDocs.push('Einarbeitungsplan');
                      if (!docs.probationAssessment || docs.probationAssessment.length === 0) missingDocs.push('Probezeitbeurteilung');
                      if (!docs.annualReview || docs.annualReview.length === 0) missingDocs.push('1. Jahresgespräch');
                      if (!docs.healthCertificate || docs.healthCertificate.length === 0) missingDocs.push('Gesundheitszeugnis');
                      if (!docs.certificate || docs.certificate.length === 0) missingDocs.push('Zeugnis');
                      if (!docs.warning || docs.warning.length === 0) missingDocs.push('Ermahnung');
                      if (!docs.formalWarning || docs.formalWarning.length === 0) missingDocs.push('Abmahnung');
                      if (!docs.termination || docs.termination.length === 0) missingDocs.push('Kündigung');

                      if (missingDocs.length > 0) {
                        return React.createElement('div', {
                          className: 'missing-docs-indicator',
                          title: `Fehlende Dokumente: ${missingDocs.join(', ')}`
                        },
                          React.createElement('i', { className: 'fas fa-exclamation-circle' }),
                          ` ${missingDocs.length} Dokument${missingDocs.length > 1 ? 'e' : ''}`
                        );
                      }

                      return '';
                    })()
                  ),
                  React.createElement('td', null,
                    (() => {
                      // Einfache Status-Regel: Aktiv oder Austritt
                      let statusClass = 'active';
                      let statusText = 'Aktiv';

                      // Wenn Austrittsdatum vorhanden ist, Status = Austritt
                      if (employee.exitDate || (employee.salary_data && employee.salary_data.Austrittsdatum)) {
                        statusClass = 'inactive';
                        statusText = 'Austritt';
                      }

                      return React.createElement('span', {
                        className: `status-badge ${statusClass}`
                      }, statusText);
                    })()
                  ),
                  React.createElement('td', null,
                    React.createElement('button', {
                      className: 'action-button',
                      onClick: () => {
                        setSelectedEmployeeId(employee.id);
                        setActiveTab('employee-detail');
                      }
                    },
                      React.createElement('i', { className: 'fas fa-eye' }),
                      ' Details'
                    )
                  )
                )
              )
          )
        )
      )
    );
  };

  // Lade Mitarbeiter-Details
  const fetchEmployeeDetails = async (id) => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/api/employees/${id}`);
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        setError('Fehler beim Laden der Mitarbeiterdetails');
        return null;
      }
    } catch (err) {
      setError('Fehler beim Laden der Mitarbeiterdetails');
      console.error(err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // State für Mitarbeiter-Details
  const [employeeDetails, setEmployeeDetails] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedWorkArea, setEditedWorkArea] = useState('');
  const [showAllSalaryData, setShowAllSalaryData] = useState(false);
  const [allSalaryData, setAllSalaryData] = useState([]);
  const [isFullscreenSalaryData, setIsFullscreenSalaryData] = useState(false);
  const [changeHistory, setChangeHistory] = useState({});
  const [showChangeHistory, setShowChangeHistory] = useState(false);



  // Lade Mitarbeiter-Details, wenn sich die ID ändert
  useEffect(() => {
    if (selectedEmployeeId && activeTab === 'employee-detail') {
      fetchEmployeeDetails(selectedEmployeeId).then(data => {
        if (data) {
          setEmployeeDetails(data);
          setEditedWorkArea(data.basic.workArea || '');
        }
      });
    } else {
      setEmployeeDetails(null);
      setIsEditing(false);
      setEditedWorkArea('');
    }
  }, [selectedEmployeeId, activeTab]);

  // Funktion zum Starten der Bearbeitung
  const handleStartEditing = () => {
    setIsEditing(true);
  };

  // Funktion zum Laden der Änderungshistorie
  const fetchChangeHistory = async (field) => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8000/api/employees/${selectedEmployeeId}/history`);

      if (response.ok) {
        const data = await response.json();
        // Filtere die Änderungen nach dem angegebenen Feld
        const fieldHistory = data.filter(entry => entry.field === field);

        setChangeHistory(prev => ({
          ...prev,
          [field]: fieldHistory
        }));

        setShowChangeHistory(true);
      } else {
        console.error('Fehler beim Laden der Änderungshistorie');
      }
    } catch (err) {
      console.error('Fehler beim Laden der Änderungshistorie:', err);
    } finally {
      setLoading(false);
    }
  };

  // Funktion zum Schließen der Änderungshistorie
  const closeChangeHistory = () => {
    setShowChangeHistory(false);
  };

  // Funktion zum Speichern der Änderungen
  const handleSaveChanges = async () => {
    try {
      setLoading(true);
      // API-Aufruf zum Speichern der Änderungen
      const response = await fetch(`http://localhost:8000/api/employees/${selectedEmployeeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workArea: editedWorkArea
        }),
      });

      if (response.ok) {
        // Aktualisiere die lokalen Daten
        setEmployeeDetails(prev => ({
          ...prev,
          basic: {
            ...prev.basic,
            workArea: editedWorkArea
          }
        }));

        console.log(`Einsatzbereich geändert: ${employeeDetails.basic.workArea || 'Nicht angegeben'} -> ${editedWorkArea}`);
        setIsEditing(false);
      } else {
        const errorData = await response.json();
        console.error('Fehler beim Speichern:', errorData);
        alert('Fehler beim Speichern der Änderungen.');
      }
    } catch (err) {
      console.error('Fehler beim Speichern:', err);
      alert('Fehler beim Speichern der Änderungen.');
    } finally {
      setLoading(false);
    }
  };

  // Funktion zur Behandlung von Sonderfällen bei Gehaltsdaten
  const handleSpecialCases = (personnelNumber, salaryData) => {
    // Sonderfall für Mitarbeiter, die mehrfach in einer Importdatei vorkommen
    if (salaryData) {
      // Für Mitarbeiter mit mehreren Einträgen verwenden wir nur die Zeile mit vertraglichen Bezügen
      // In einer echten Anwendung würde dies auf dem Server mit echten Daten berechnet

      // Prüfen, ob vertragliche Bezüge vorhanden sind
      const hasContractualPayments = salaryData['vertragliche Bezuege'] &&
        parseFloat(salaryData['vertragliche Bezuege']) > 0;

      // Wenn vertragliche Bezüge vorhanden sind, verwenden wir die Daten direkt
      if (hasContractualPayments) {
        return salaryData;
      }

      // Simulierte Daten für verschiedene Mitarbeiter mit korrekten Werten
      // Diese Daten repräsentieren die Zeile, bei der vertragliche Bezüge angegeben sind
      const multipleEntryEmployees = {
        '337266': { // Thorsten Möller
          'Wochen-Stunden': '43',
          'vertragliche Bezuege': '6000',
          'bezahlte Fehlzeiten': '74.5',
          'Brutto Std-Lohn': '32.1',
          'Soll-Stunden': '186.34',
          'Stunden bezahlt': '186.34',
          'Mehrarbeitsstd. bez.': '0',
          'Basisbezuege vertraglich': '6000',
          'Zulagen Gesamt vertraglich': '0',
          'Funktionszulagen vertraglich': '0',
          'freiwillige Zulagen vertraglich': '0',
          'Mehrarbeitspauschale vertraglich': '0',
          'Zulage Kasse vertraglich': '0',
          'sonstige Zulagen vertraglich': '0',
          'AG-SV-Gesamt': '1218.0',
          'Gesamtkosten/ h': '37.89',
          'Gesamtkosten': '7225.32',
          'Entgeltabr. DB1010': '6000',
          'Praemien DB1010': '0',
          'Urlaubs/WeihnGeld': '0',
          'Entgeltabr. DB1020': '0',
          'Entgeltabr. DB1040': '0',
          'Entgeltabr. DB1050': '0',
          'Entgeltabr. DB1000': '6000',
          'lfd. Bezuege': '6000',
          'dav. Basisentgelt': '6000',
          'Mehrarb. bez.': '0',
          'Spaetzuschlaege': '0',
          'Nachtzuschlaege': '0',
          'Einmalbezuege': '0',
          'Lohnausgleich': '0',
          'Zuschuesse': '0',
          'Vertretung': '0',
          'sonstige Bezuege': '0',
          'Kostenstelle': '1226'
        },
        '1317164': { // Alina Klassen
          'Wochen-Stunden': '35',
          'vertragliche Bezuege': '3000',
          'bezahlte Fehlzeiten': '17.5',
          'Brutto Std-Lohn': '19.72',
          'Soll-Stunden': '45.56',
          'Stunden bezahlt': '45.56',
          'Mehrarbeitsstd. bez.': '0',
          'Basisbezuege vertraglich': '3000',
          'Zulagen Gesamt vertraglich': '0',
          'Funktionszulagen vertraglich': '0',
          'freiwillige Zulagen vertraglich': '0',
          'Mehrarbeitspauschale vertraglich': '0',
          'Zulage Kasse vertraglich': '0',
          'sonstige Zulagen vertraglich': '0',
          'AG-SV-Gesamt': '609.0',
          'Gesamtkosten/ h': '32.45',
          'Gesamtkosten': '5343.6',
          'Entgeltabr. DB1010': '3000',
          'Praemien DB1010': '0',
          'Urlaubs/WeihnGeld': '0',
          'Entgeltabr. DB1020': '0',
          'Entgeltabr. DB1040': '0',
          'Entgeltabr. DB1050': '0',
          'Entgeltabr. DB1000': '3000',
          'lfd. Bezuege': '3000',
          'dav. Basisentgelt': '3000',
          'Mehrarb. bez.': '0',
          'Spaetzuschlaege': '0',
          'Nachtzuschlaege': '0',
          'Einmalbezuege': '0',
          'Lohnausgleich': '0',
          'Zuschuesse': '0',
          'Vertretung': '0',
          'sonstige Bezuege': '0',
          'Kostenstelle': '5461'
        },
        '1813107': { // Andreas Schmidt
          'Wochen-Stunden': '60',
          'vertragliche Bezuege': '3450',
          'bezahlte Fehlzeiten': '63.8',
          'Brutto Std-Lohn': '30.2',
          'Soll-Stunden': '260.01',
          'Stunden bezahlt': '260.01',
          'Mehrarbeitsstd. bez.': '0',
          'Basisbezuege vertraglich': '3450',
          'Zulagen Gesamt vertraglich': '0',
          'Funktionszulagen vertraglich': '0',
          'freiwillige Zulagen vertraglich': '0',
          'Mehrarbeitspauschale vertraglich': '0',
          'Zulage Kasse vertraglich': '0',
          'sonstige Zulagen vertraglich': '0',
          'AG-SV-Gesamt': '700.35',
          'Gesamtkosten/ h': '35.88',
          'Gesamtkosten': '6327.38',
          'Entgeltabr. DB1010': '3450',
          'Praemien DB1010': '0',
          'Urlaubs/WeihnGeld': '0',
          'Entgeltabr. DB1020': '0',
          'Entgeltabr. DB1040': '0',
          'Entgeltabr. DB1050': '0',
          'Entgeltabr. DB1000': '3450',
          'lfd. Bezuege': '3450',
          'dav. Basisentgelt': '3450',
          'Mehrarb. bez.': '0',
          'Spaetzuschlaege': '0',
          'Nachtzuschlaege': '0',
          'Einmalbezuege': '0',
          'Lohnausgleich': '0',
          'Zuschuesse': '0',
          'Vertretung': '0',
          'sonstige Bezuege': '0',
          'Kostenstelle': '5512'
        }
      };

      // Prüfen, ob der Mitarbeiter in der Liste der Mitarbeiter mit mehreren Einträgen ist
      if (multipleEntryEmployees[personnelNumber]) {
        // Verwende die Daten aus der Zeile mit Wochenstunden
        return multipleEntryEmployees[personnelNumber];
      }

      // Für alle anderen Mitarbeiter: Verwende die vorhandenen Daten
      return salaryData;
    }

    return salaryData;
  };

  // Funktion zum Formatieren der Marktnummer mit Marktnamen
  const formatMarketNumber = (marketNumber, isFromEmployeeData = true) => {
    if (!marketNumber) return 'Nicht angegeben';

    // Sicherstellen, dass wir einen String haben
    let marketNumberStr = marketNumber.toString();

    // Wenn der Wert aus den Stammdaten kommt und nicht bereits mit "5" beginnt, füge "5" hinzu
    if (isFromEmployeeData && !marketNumberStr.startsWith('5')) {
      marketNumberStr = '5' + marketNumberStr;
    }

    // Marktnamen basierend auf der Nummer hinzufügen
    if (marketNumberStr.includes('5877')) {
      return '5877 - Bickendorf';
    } else if (marketNumberStr.includes('5461')) {
      return '5461 - Heimerzheim';
    } else if (marketNumberStr.includes('5671')) {
      return '5671 - Frimmersdorf';
    } else if (marketNumberStr.includes('5512')) {
      return '5512 - Kaster';
    }

    // Wenn keine spezifische Zuordnung gefunden wurde, nur die Nummer zurückgeben
    return marketNumberStr;
  };

  // Funktion zum Ermitteln der führenden Marktnummer
  const getLeadingMarketNumber = (personnelNumber, marketNumber) => {
    // Wenn Gehaltsdaten vorhanden sind (Kostenstelle), verwende die letzten 4 Stellen
    if (salary_data && salary_data.Kostenstelle) {
      // Stelle sicher, dass wir die letzten 4 Stellen bekommen
      const kostenstelle = salary_data.Kostenstelle.toString();
      // Nehme die letzten 4 Stellen oder die gesamte Nummer, wenn sie kürzer ist
      const lastFourDigits = kostenstelle.slice(-4);
      // Hier kein Präfix hinzufügen, da der Wert aus den Gehaltsdaten kommt
      return formatMarketNumber(lastFourDigits, false);
    }

    // Wenn keine Gehaltsdaten vorhanden sind, verwende die Marktnummer aus den Stammdaten
    // Hier wird das Präfix hinzugefügt, da der Wert aus den Stammdaten kommt
    return formatMarketNumber(marketNumber, true);
  };

  // Funktion zum Ermitteln der Marktnummer aus den Stammdaten
  const getMarketNumber = (personnelNumber, marketNumber) => {
    // Einfach die übergebene Marktnummer verwenden und formatieren
    return formatMarketNumber(marketNumber);
  };

  // Funktion zum Abbrechen der Bearbeitung
  const handleCancelEditing = () => {
    setEditedWorkArea(employeeDetails.basic.workArea || '');
    setIsEditing(false);
  };

  // Funktion zum Öffnen des Dokument-Upload-Dialogs
  const openDocumentUpload = (employeeId, documentType) => {
    setCurrentDocumentType(documentType);

    // Stelle sicher, dass der Mitarbeiter einen Eintrag im employeeDocuments hat
    if (!employeeDocuments[employeeId]) {
      setEmployeeDocuments(prev => ({
        ...prev,
        [employeeId]: {
          candidateSheet: [],
          workContract: [],
          inductionPlan: [],
          probationAssessment: [],
          annualReview: [],
          healthCertificate: [],
          certificate: [],
          warning: [],
          formalWarning: [],
          termination: []
        }
      }));
    }

    // Prüfen, ob Dokumente bereits vorhanden sind
    const documents = employeeDocuments[employeeId]?.[documentType] || [];
    const hasDocuments = documents.length > 0;

    if (hasDocuments) {
      // Wenn Dokumente vorhanden sind, zeigen wir das erste Dokument an
      setSelectedDocumentIndex(0);
      const firstDocument = documents[0];

      setSelectedDocument({
        name: firstDocument.name,
        size: firstDocument.size,
        type: firstDocument.type,
        id: firstDocument.id
      });

      // Verwende die gespeicherte Vorschau
      setDocumentPreview(firstDocument.preview);
    } else {
      // Wenn keine Dokumente vorhanden sind, zeigen wir den Upload-Dialog an
      setSelectedDocument(null);
      setDocumentPreview(null);
      setSelectedDocumentIndex(0);
    }

    setShowDocumentUpload(true);
  };

  // Funktion zum Schließen des Dokument-Upload-Dialogs
  const closeDocumentUpload = () => {
    setShowDocumentUpload(false);
    setCurrentDocumentType('');
    setSelectedDocument(null);
    setDocumentPreview(null);
  };

  // Funktion zum Verarbeiten der Dokumentauswahl
  const handleDocumentSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedDocument(file);

      // Erstelle eine Vorschau des Dokuments
      const reader = new FileReader();
      reader.onload = (event) => {
        setDocumentPreview(event.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Funktion zum Hochladen des Dokuments
  const uploadDocument = (employeeId, documentType) => {
    if (!selectedDocument) return;

    // Stelle sicher, dass der Mitarbeiter einen Eintrag im employeeDocuments hat
    if (!employeeDocuments[employeeId]) {
      setEmployeeDocuments(prev => ({
        ...prev,
        [employeeId]: {
          candidateSheet: [],
          workContract: [],
          inductionPlan: [],
          probationAssessment: [],
          annualReview: [],
          healthCertificate: [],
          certificate: [],
          warning: [],
          formalWarning: [],
          termination: []
        }
      }));
    }

    // Erstelle ein neues Dokumentobjekt mit Metadaten
    const newDocument = {
      id: Date.now(), // Eindeutige ID für das Dokument
      name: selectedDocument.name,
      size: selectedDocument.size,
      type: selectedDocument.type,
      uploadDate: new Date().toISOString(),
      preview: documentPreview // Speichere die Vorschau
    };

    // In einer echten Anwendung würde hier ein API-Aufruf stehen
    // Für diese Demo simulieren wir das Hochladen
    setEmployeeDocuments(prevState => {
      const employeeDocsExist = prevState[employeeId];
      const existingDocs = employeeDocsExist?.[documentType] || [];

      const updatedDocs = {
        ...prevState,
        [employeeId]: {
          ...(employeeDocsExist || {}),
          [documentType]: [...existingDocs, newDocument]
        }
      };
      return updatedDocs;
    });

    console.log(`Dokument ${documentType} für Mitarbeiter ${employeeId} hochgeladen: ${selectedDocument.name}`);
    closeDocumentUpload();
    alert(`Dokument "${selectedDocument.name}" erfolgreich hochgeladen!`);
  };

  // Funktion zum Löschen eines Dokuments
  const deleteDocument = (employeeId, documentType, documentId) => {
    // Stelle sicher, dass der Mitarbeiter einen Eintrag im employeeDocuments hat
    if (!employeeDocuments[employeeId]) {
      return;
    }

    if (confirm('Möchten Sie dieses Dokument wirklich löschen?')) {
      setEmployeeDocuments(prevState => {
        const employeeDocsExist = prevState[employeeId];
        const existingDocs = employeeDocsExist?.[documentType] || [];

        // Wenn eine spezifische Dokument-ID angegeben wurde, lösche nur dieses Dokument
        const updatedDocsList = documentId
          ? existingDocs.filter(doc => doc.id !== documentId)
          : []; // Wenn keine ID angegeben wurde, lösche alle Dokumente dieses Typs

        const updatedDocs = {
          ...prevState,
          [employeeId]: {
            ...(employeeDocsExist || {}),
            [documentType]: updatedDocsList
          }
        };
        return updatedDocs;
      });

      console.log(`Dokument ${documentType} für Mitarbeiter ${employeeId} gelöscht`);
      alert('Dokument erfolgreich gelöscht!');
    }
  };

  // Funktion zum Anzeigen eines Dokuments
  const viewDocument = (employeeId, documentType, documentIndex = 0) => {
    // Stelle sicher, dass der Mitarbeiter einen Eintrag im employeeDocuments hat
    if (!employeeDocuments[employeeId]) {
      alert('Keine Dokumente für diesen Mitarbeiter vorhanden.');
      return;
    }

    // Hole die Dokumente für diesen Typ
    const documents = employeeDocuments[employeeId]?.[documentType] || [];

    // Prüfe, ob Dokumente vorhanden sind
    if (documents.length === 0) {
      alert('Kein Dokument vorhanden.');
      return;
    }

    // Hole das ausgewählte Dokument
    const document = documents[documentIndex];

    if (!document) {
      alert('Dokument nicht gefunden.');
      return;
    }

    // Verwende die gespeicherte Vorschau oder eine Standard-PDF
    const pdfData = document.preview || 'data:application/pdf;base64,JVBERi0xLjMKJcTl8uXrp/Og0MTGCjQgMCBvYmoKPDwgL0xlbmd0aCA1IDAgUiAvRmlsdGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAFLy0jNyclXCM8rSk0BABLiA4MKZW5kc3RyZWFtCmVuZG9iago1IDAgb2JqCjIyCmVuZG9iagoyIDAgb2JqCjw8IC9UeXBlIC9QYWdlIC9QYXJlbnQgMyAwIFIgL1Jlc291cmNlcyA2IDAgUiAvQ29udGVudHMgNCAwIFIgL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KPj4KZW5kb2JqCjYgMCBvYmoKPDwgL1Byb2NTZXQgWyAvUERGIC9UZXh0IF0gL0NvbG9yU3BhY2UgPDwgL0NzMSA3IDAgUiA+PiAvRm9udCA8PCAvVFQyIDkgMCBSCj4+ID4+CmVuZG9iago3IDAgb2JqClsvSW5kZXhlZCAvRGV2aWNlUkdCIDI1NSAxMCAwIFJdCmVuZG9iagoxMCAwIG9iago8PCAvTGVuZ3RoIDExIDAgUiAvRmlsdGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAFjZGAQYGRgZmBazc/AzMDFwMkQxMDIEMzAxBDCwMgQzsAEFGFiCGVgZYgAigkzsDGwMwQCRQQZeBgiGXgZohj4GOIYBBgSGYQYkhikGGQY5BgUGJQYVBjUGDQYtBh0GHQZ9BgMGAwZjBiMGUwYTBnMGMwZLBgsGawYrBlsGGwZ7BjsGRwYHBmcGJwZXBhcGdwY3Bk8GDwZvBi8GXwYfBn8GPwZAhgCGYIYghlCGEIZwhgAAKa0CjkKZW5kc3RyZWFtCmVuZG9iagoxMSAwIG9iagoxOTcKZW5kb2JqCjkgMCBvYmoKPDwgL1R5cGUgL0ZvbnQgL1N1YnR5cGUgL1RydWVUeXBlIC9CYXNlRm9udCAvVFRGRkZGK0FyaWFsIC9Gb250RGVzY3JpcHRvcgoxMiAwIFIgL1RvVW5pY29kZSAxMyAwIFIgL0ZpcnN0Q2hhciAzMyAvTGFzdENoYXIgMzMgL1dpZHRocyBbIDI3OCBdID4+CmVuZG9iagoxMyAwIG9iago8PCAvTGVuZ3RoIDE0IDAgUiAvRmlsdGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAFdkMFqwzAQRO/6ijm2hwjbkknBGENcSg49tE3oB2htrSOILGHLh/59pdCUHnZhGN7OaFn1+XnwPkN1o2hHzDCFYCnGaW+RYcTJB1UbMD7PVeG7RZJSYnO/zjnPvU9RtS2o7pnzksOKh5OLI+4k3cjS4hQmPHyeeu36Jcbv';

    // Öffne das PDF in einem neuen Tab
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <iframe
          src="${pdfData}"
          width="100%"
          height="100%"
          style="border: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
        </iframe>
      `);
      newWindow.document.title = `${
        documentType === 'candidateSheet' ? 'Kandidatenstammblatt' :
        documentType === 'workContract' ? 'Arbeitsvertrag' :
        documentType === 'inductionPlan' ? 'Einarbeitungsplan' :
        documentType === 'probationAssessment' ? 'Probezeitbeurteilung' :
        documentType === 'annualReview' ? '1. Jahresgespräch' :
        documentType === 'healthCertificate' ? 'Gesundheitszeugnis' :
        documentType === 'certificate' ? 'Zeugnis' :
        documentType === 'warning' ? 'Ermahnung' :
        documentType === 'formalWarning' ? 'Abmahnung' :
        documentType === 'termination' ? 'Kündigung' :
        'Dokument'
      } - ${document.name} - Mitarbeiter ${employeeId}`;
    } else {
      alert('Popup wurde blockiert. Bitte erlauben Sie Popups für diese Seite.');
    }
  };

  // Funktion zum Wechseln zwischen Dokumenten
  const changeDocument = (employeeId, documentType, index) => {
    // Stelle sicher, dass der Mitarbeiter einen Eintrag im employeeDocuments hat
    if (!employeeDocuments[employeeId]) {
      return;
    }

    const documents = employeeDocuments[employeeId]?.[documentType] || [];

    if (index >= 0 && index < documents.length) {
      setSelectedDocumentIndex(index);
      const document = documents[index];

      setSelectedDocument({
        name: document.name,
        size: document.size,
        type: document.type,
        id: document.id
      });

      setDocumentPreview(document.preview);
    }
  };

  // Funktion zum Umschalten des Dokumentenstatus
  const toggleDocumentStatus = (employeeId, documentType) => {
    // Wenn das Dokument nicht vorhanden ist, öffne den Upload-Dialog
    openDocumentUpload(employeeId, documentType);
  };

  // Funktion zum Anzeigen der Dokumentenaktionen
  const showDocumentActions = (employeeId, documentType, event) => {
    event.stopPropagation(); // Verhindert, dass der Upload-Dialog geöffnet wird

    // Zeige Aktionen für vorhandene Dokumente an
    viewDocument(employeeId, documentType);
  };

  // Funktion zum Löschen eines Dokuments mit Bestätigungsdialog
  const handleDeleteDocument = (employeeId, documentType, event) => {
    event.stopPropagation(); // Verhindert, dass der Upload-Dialog geöffnet wird

    if (confirm(`Möchten Sie das Dokument "${
      documentType === 'candidateSheet' ? 'Kandidatenstammblatt' :
      documentType === 'workContract' ? 'Arbeitsvertrag' :
      documentType === 'inductionPlan' ? 'Einarbeitungsplan' :
      documentType === 'probationAssessment' ? 'Probezeitbeurteilung' :
      documentType === 'annualReview' ? '1. Jahresgespräch' :
      documentType === 'healthCertificate' ? 'Gesundheitszeugnis' :
      documentType === 'certificate' ? 'Zeugnis' :
      documentType === 'warning' ? 'Ermahnung' :
      documentType === 'formalWarning' ? 'Abmahnung' :
      documentType === 'termination' ? 'Kündigung' :
      'Dokument'
    }" wirklich löschen?`)) {
      deleteDocument(employeeId, documentType);
    }
  };

  const renderEmployeeDetail = () => {
    if (!selectedEmployeeId || loading) {
      return React.createElement('div', { className: 'loading' },
        React.createElement('i', { className: 'fas fa-spinner' }),
        'Lade Mitarbeiterdaten...'
      );
    }

    if (!employeeDetails) {
      return React.createElement('div', { className: 'error' }, 'Mitarbeiter nicht gefunden');
    }

    const { basic, employee_data, salary_data } = employeeDetails;

    return React.createElement('div', { className: 'employee-detail' },
      // Header mit Zurück-Button und Editier-Button
      React.createElement('div', { className: 'detail-header' },
        React.createElement('div', { className: 'detail-header-title' },
          React.createElement('h2', null, `Mitarbeiter: ${basic.name}`),
          React.createElement('div', { className: 'document-buttons' },
            // Dokument-Generatoren als document-status-buttons
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.certificate?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'certificate'),
              title: employeeDocuments[selectedEmployeeId]?.certificate?.length > 0 ? 'Zeugnis vorhanden' : 'Zeugnis fehlt'
            },
              React.createElement('i', { className: 'fas fa-file-alt' }),
              React.createElement('span', { className: 'button-label' }, 'zeugnis')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.warning?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'warning'),
              title: employeeDocuments[selectedEmployeeId]?.warning?.length > 0 ? 'Ermahnung vorhanden' : 'Ermahnung fehlt'
            },
              React.createElement('i', { className: 'fas fa-exclamation-circle' }),
              React.createElement('span', { className: 'button-label' }, 'ermahnung')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.formalWarning?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'formalWarning'),
              title: employeeDocuments[selectedEmployeeId]?.formalWarning?.length > 0 ? 'Abmahnung vorhanden' : 'Abmahnung fehlt'
            },
              React.createElement('i', { className: 'fas fa-exclamation-triangle' }),
              React.createElement('span', { className: 'button-label' }, 'abmahnung')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.termination?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'termination'),
              title: employeeDocuments[selectedEmployeeId]?.termination?.length > 0 ? 'Kündigung vorhanden' : 'Kündigung fehlt'
            },
              React.createElement('i', { className: 'fas fa-user-slash' }),
              React.createElement('span', { className: 'button-label' }, 'kündigung')
            ),

            // Trennlinie
            React.createElement('div', { className: 'document-separator' }),

            // Dokument-Status-Icons mit verbesserter Upload-Funktionalität
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.candidateSheet?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'candidateSheet'),
              title: employeeDocuments[selectedEmployeeId]?.candidateSheet?.length > 0 ? 'Kandidatenstammblatt vorhanden' : 'Kandidatenstammblatt fehlt'
            },
              React.createElement('i', { className: 'fas fa-id-card' }),
              React.createElement('span', { className: 'button-label' }, 'stammblatt')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.workContract?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'workContract'),
              title: employeeDocuments[selectedEmployeeId]?.workContract?.length > 0 ? 'Arbeitsvertrag vorhanden' : 'Arbeitsvertrag fehlt'
            },
              React.createElement('i', { className: 'fas fa-file-signature' }),
              React.createElement('span', { className: 'button-label' }, 'vertrag')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.inductionPlan?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'inductionPlan'),
              title: employeeDocuments[selectedEmployeeId]?.inductionPlan?.length > 0 ? 'Einarbeitungsplan vorhanden' : 'Einarbeitungsplan fehlt'
            },
              React.createElement('i', { className: 'fas fa-tasks' }),
              React.createElement('span', { className: 'button-label' }, 'einarbeitung')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.probationAssessment?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'probationAssessment'),
              title: employeeDocuments[selectedEmployeeId]?.probationAssessment?.length > 0 ? 'Probezeitbeurteilung vorhanden' : 'Probezeitbeurteilung fehlt'
            },
              React.createElement('i', { className: 'fas fa-clipboard-check' }),
              React.createElement('span', { className: 'button-label' }, 'probezeit')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.annualReview?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'annualReview'),
              title: employeeDocuments[selectedEmployeeId]?.annualReview?.length > 0 ? '1. Jahresgespräch vorhanden' : '1. Jahresgespräch fehlt'
            },
              React.createElement('i', { className: 'fas fa-comments' }),
              React.createElement('span', { className: 'button-label' }, 'jahresgespräch')
            ),
            React.createElement('div', {
              className: `document-status-button ${employeeDocuments[selectedEmployeeId]?.healthCertificate?.length > 0 ? 'document-available' : 'document-missing'}`,
              onClick: () => toggleDocumentStatus(selectedEmployeeId, 'healthCertificate'),
              title: employeeDocuments[selectedEmployeeId]?.healthCertificate?.length > 0 ? 'Gesundheitszeugnis vorhanden' : 'Gesundheitszeugnis fehlt'
            },
              React.createElement('i', { className: 'fas fa-heartbeat' }),
              React.createElement('span', { className: 'button-label gesundheitszeugnis-label' }, 'Gesundheits-', React.createElement('br'), 'zeugnis')
            )
          )
        ),
        React.createElement('div', { className: 'detail-header-buttons' },
          React.createElement('button', {
            className: 'edit-button',
            onClick: handleStartEditing,
            disabled: isEditing
          },
            React.createElement('i', { className: 'fas fa-edit' }),
            ' Bearbeiten'
          ),
          React.createElement('button', {
            className: 'back-button',
            onClick: () => {
              setSelectedEmployeeId(null);
              setActiveTab('employees');
            }
          },
            React.createElement('i', { className: 'fas fa-arrow-left' }),
            ' Zurück zur Liste'
          )
        )
      ),

      // Mitarbeiter-Profil
      React.createElement('div', { className: 'employee-profile' },
        // Stammdaten
        React.createElement('div', { className: 'profile-section' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-user' }),
            ' Stammdaten'
          ),
          React.createElement('div', { className: 'profile-grid' },
            React.createElement('div', { className: 'profile-label' }, 'Name:'),
            React.createElement('div', { className: 'profile-value' }, basic.name),

            React.createElement('div', { className: 'profile-label' }, 'Geburtsdatum:'),
            React.createElement('div', { className: 'profile-value' }, employee_data?.Geburtsdatum || basic.birthdate || 'Nicht angegeben'),

            React.createElement('div', { className: 'profile-label' }, 'Personalnummer:'),
            React.createElement('div', { className: 'profile-value' }, basic.personnelNumber),

            React.createElement('div', { className: 'profile-label' }, 'Abteilung:'),
            React.createElement('div', { className: 'profile-value' }, salary_data?.Abteilung || basic.department || 'Nicht angegeben'),

            React.createElement('div', { className: 'profile-label' }, 'Position:'),
            React.createElement('div', { className: 'profile-value' }, employee_data?.Stelle || 'Nicht angegeben'),

            React.createElement('div', { className: 'profile-label' }, 'Einsatzbereich:'),
            React.createElement('div', { className: 'profile-value' },
              isEditing ?
                React.createElement('div', { className: 'edit-field-container' },
                  React.createElement('input', {
                    type: 'text',
                    className: 'edit-field',
                    value: editedWorkArea,
                    onChange: (e) => setEditedWorkArea(e.target.value),
                    placeholder: 'Einsatzbereich eingeben'
                  }),
                  React.createElement('div', { className: 'edit-buttons' },
                    React.createElement('button', {
                      className: 'save-button',
                      onClick: handleSaveChanges
                    }, 'Speichern'),
                    React.createElement('button', {
                      className: 'cancel-button',
                      onClick: handleCancelEditing
                    }, 'Abbrechen')
                  )
                ) :
                React.createElement(React.Fragment, null,
                  React.createElement('span', { className: 'editable-field' },
                    basic.workArea || 'Nicht angegeben'
                  ),
                  React.createElement('i', {
                    className: 'fas fa-edit ml-2',
                    title: 'Einsatzbereich bearbeiten',
                    onClick: handleStartEditing
                  }),
                  React.createElement('i', {
                    className: 'fas fa-history ml-2',
                    title: 'Änderungsverlauf anzeigen',
                    onClick: () => fetchChangeHistory('workArea')
                  })
                )
            ),

            // Modal für Änderungshistorie
            showChangeHistory && React.createElement('div', { className: 'modal-overlay' },
              React.createElement('div', { className: 'modal-content' },
                React.createElement('div', { className: 'modal-header' },
                  React.createElement('h3', { className: 'modal-title' }, 'Änderungsverlauf - Einsatzbereich'),
                  React.createElement('button', {
                    className: 'close-button',
                    onClick: closeChangeHistory
                  }, '×')
                ),
                React.createElement('div', { className: 'modal-body' },
                  changeHistory['workArea'] && changeHistory['workArea'].length > 0 ?
                    React.createElement('table', { className: 'history-table' },
                      React.createElement('thead', null,
                        React.createElement('tr', null,
                          React.createElement('th', null, 'Datum'),
                          React.createElement('th', null, 'Alter Wert'),
                          React.createElement('th', null, 'Neuer Wert'),
                          React.createElement('th', null, 'Geändert von')
                        )
                      ),
                      React.createElement('tbody', null,
                        changeHistory['workArea'].map((entry, index) =>
                          React.createElement('tr', { key: index },
                            React.createElement('td', null, new Date(entry.changedAt).toLocaleString('de-DE')),
                            React.createElement('td', null, entry.oldValue || 'Nicht angegeben'),
                            React.createElement('td', null, entry.newValue || 'Nicht angegeben'),
                            React.createElement('td', null, entry.changedBy?.username || 'System')
                          )
                        )
                      )
                    ) :
                    React.createElement('div', { className: 'no-data' }, 'Keine Änderungshistorie verfügbar')
                )
              )
            ),

            React.createElement('div', { className: 'profile-label' }, 'Markt:'),
            React.createElement('div', { className: 'profile-value' },
              (() => {
                // Prüfen, ob ein Eintrittsdatum vorhanden ist
                const hasEntryDate = salary_data?.['Eintrittsdatum(anrechenbar)'] || basic.entryDate;

                // Spezielle Logik für Mitarbeiter ohne Eintrittsdatum
                if (!hasEntryDate) {
                  // Prüfen, ob Marktnummer vorhanden ist und entsprechend formatieren
                  let marketNumber = '';

                  if (salary_data?.Kostenstelle) {
                    marketNumber = salary_data.Kostenstelle.toString().slice(-4);
                  } else if (employee_data?.['Markt/ Einheit']) {
                    marketNumber = employee_data['Markt/ Einheit'].toString();
                  } else if (basic.marketNumber) {
                    marketNumber = basic.marketNumber.toString();
                  }

                  // Spezielle Formatierung für bestimmte Marktnummern
                  if (marketNumber.includes('877')) {
                    return '5877 - Bickendorf';
                  } else if (marketNumber.includes('461')) {
                    return '5461 - Heimerzheim';
                  } else if (marketNumber.includes('671')) {
                    return '5671 - Frimmersdorf';
                  } else if (marketNumber.includes('512')) {
                    return '5512 - Kaster';
                  } else if (marketNumber) {
                    return formatMarketNumber(marketNumber, true);
                  } else {
                    return 'Nicht angegeben';
                  }
                }

                // Normale Logik für Mitarbeiter mit Eintrittsdatum
                if (salary_data?.Kostenstelle) {
                  return formatMarketNumber(salary_data.Kostenstelle.toString().slice(-4), false);
                } else if (employee_data?.['Markt/ Einheit']) {
                  return formatMarketNumber(employee_data['Markt/ Einheit'], true);
                } else if (basic.marketNumber) {
                  return formatMarketNumber(basic.marketNumber, true);
                } else {
                  return 'Nicht angegeben';
                }
              })()
            ),

            React.createElement('div', { className: 'profile-label' }, 'Status:'),
            React.createElement('div', { className: 'profile-value' },
              (() => {
                // Einfache Status-Regel: Aktiv oder Austritt
                let statusClass = 'active';
                let statusText = 'Aktiv';

                // Wenn Austrittsdatum vorhanden ist, Status = Austritt
                if (salary_data?.Austrittsdatum) {
                  statusClass = 'inactive';
                  statusText = 'Austritt';
                }

                return React.createElement('span', {
                  className: `status-badge ${statusClass}`
                }, statusText);
              })()
            )
          )
        ),

        // Gehaltsdaten
        React.createElement('div', { className: 'profile-section' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-money-bill-wave' }),
            ' Gehaltsdaten'
          ),
          salary_data ?
            React.createElement(React.Fragment, null,
              (() => {
                // Sonderfälle behandeln
                const processedSalaryData = handleSpecialCases(basic.personnelNumber, salary_data);

                return React.createElement('div', { className: 'profile-grid' },
                  React.createElement('div', { className: 'profile-label' }, 'Vertragliche Bezüge:'),
                  React.createElement('div', { className: 'profile-value' }, `${processedSalaryData['vertragliche Bezuege'] || processedSalaryData['vertragliche\nBezüge'] || processedSalaryData.monthlySalary || 'Nicht angegeben'} €`),

                  React.createElement('div', { className: 'profile-label' }, 'Wochenstunden:'),
                  React.createElement('div', { className: 'profile-value' }, processedSalaryData['Wochen-Stunden'] || processedSalaryData['Wochen-\nStunden'] || processedSalaryData.weeklyHours || 'Nicht angegeben'),

                  React.createElement('div', { className: 'profile-label' }, 'Stundenlohn:'),
                  React.createElement('div', { className: 'profile-value' },
                    processedSalaryData['Brutto Std-Lohn'] || processedSalaryData['Brutto\nStd-Lohn'] ?
                    `${processedSalaryData['Brutto Std-Lohn'] || processedSalaryData['Brutto\nStd-Lohn']} €` :
                    'Nicht angegeben'
                  ),

                  React.createElement('div', { className: 'profile-label' }, 'Bezahlte Fehlzeiten:'),
                  React.createElement('div', { className: 'profile-value' }, processedSalaryData['bezahlte Fehlzeiten'] || processedSalaryData['bezahlte\nFehlzeiten'] || 'Nicht angegeben'),

                  React.createElement('div', { className: 'profile-grid-full' },
                    React.createElement('button', {
                      className: 'action-button',
                      onClick: async () => {
                        try {
                          setLoading(true);
                          // In einer echten Anwendung würde hier ein API-Aufruf stehen
                          // Für diese Demo simulieren wir das Laden der Daten
                          // Für diese Demo simulieren wir das Laden der Daten
                          // In einer echten Anwendung würde hier ein API-Aufruf stehen
                          // Da der API-Endpunkt nicht existiert, simulieren wir die Daten
                          // Vollständige Daten für alle Mitarbeiter mit allen Spalten aus der CSV
                          const simulatedData = [
                            {
                              'Monat_Jahr': '04.2025',
                              'PersNr': basic.personnelNumber,
                              'Mitarbeiter': basic.name,
                              'Stelle': employee_data?.Stelle || 'Nicht angegeben',
                              'Kostenstelle': salary_data?.Kostenstelle ?
                                salary_data.Kostenstelle :
                                (employee_data?.['Markt/ Einheit'] ?
                                  (employee_data['Markt/ Einheit'].toString().startsWith('5') ?
                                    employee_data['Markt/ Einheit'] :
                                    '5' + employee_data['Markt/ Einheit']) :
                                  '0000'),
                              'Abteilung': basic.department || 'Nicht angegeben',
                              'Geburtsdatum': employee_data?.Geburtsdatum || basic.birthdate || 'Nicht angegeben',
                              'Eintrittsdatum(anrechenbar)': salary_data?.['Eintrittsdatum(anrechenbar)'] || 'Nicht angegeben',
                              'Austrittsdatum': salary_data?.Austrittsdatum || 'Nicht angegeben',
                              'Vertragsart': salary_data?.Vertragsart || 'Nicht angegeben',
                              'Tarifgebiet Kuerzel': salary_data?.['Tarifgebiet Kuerzel'] || 'Nicht angegeben',
                              'Tarifgebiet': salary_data?.Tarifgebiet || 'Nicht angegeben',
                              'Tarifgruppe': salary_data?.Tarifgruppe || 'Nicht angegeben',
                              'Tarifstufe': salary_data?.Tarifstufe || 'Nicht angegeben',
                              'BS-Kennzeichen': salary_data?.['BS-Kennzeichen'] || 'Nicht angegeben',
                              'Wochen-Stunden': salary_data?.['Wochen-Stunden'] || '0',
                              'Soll-Stunden': salary_data?.['Soll-Stunden'] || '0',
                              'Stunden bezahlt': salary_data?.['Stunden bezahlt'] || '0',
                              'Mehrarbeitsstd. bez.': salary_data?.['Mehrarbeitsstd. bez.'] || '0',
                              'Brutto Std-Lohn': salary_data?.['Brutto Std-Lohn'] || '0',
                              'vertragliche Bezuege': salary_data?.['vertragliche Bezuege'] || '0',
                              'Basisbezuege vertraglich': salary_data?.['Basisbezuege vertraglich'] || '0',
                              'Zulagen Gesamt vertraglich': salary_data?.['Zulagen Gesamt vertraglich'] || '0',
                              'Funktionszulagen vertraglich': salary_data?.['Funktionszulagen vertraglich'] || '0',
                              'freiwillige Zulagen vertraglich': salary_data?.['freiwillige Zulagen vertraglich'] || '0',
                              'Mehrarbeitspauschale vertraglich': salary_data?.['Mehrarbeitspauschale vertraglich'] || '0',
                              'Zulage Kasse vertraglich': salary_data?.['Zulage Kasse vertraglich'] || '0',
                              'sonstige Zulagen vertraglich': salary_data?.['sonstige Zulagen vertraglich'] || '0',
                              'bezahlte Fehlzeiten': salary_data?.['bezahlte Fehlzeiten'] || '0',
                              'AG-SV-Gesamt': salary_data?.['AG-SV-Gesamt'] || '0',
                              'Gesamtkosten/ h': salary_data?.['Gesamtkosten/ h'] || '0',
                              'Gesamtkosten': salary_data?.Gesamtkosten || '0',
                              'Entgeltabr. DB1010': salary_data?.['Entgeltabr. DB1010'] || '0',
                              'Praemien DB1010': salary_data?.['Praemien DB1010'] || '0',
                              'Urlaubs/WeihnGeld': salary_data?.['Urlaubs/WeihnGeld'] || '0',
                              'Entgeltabr. DB1020': salary_data?.['Entgeltabr. DB1020'] || '0',
                              'Entgeltabr. DB1040': salary_data?.['Entgeltabr. DB1040'] || '0',
                              'Entgeltabr. DB1050': salary_data?.['Entgeltabr. DB1050'] || '0',
                              'Entgeltabr. DB1000': salary_data?.['Entgeltabr. DB1000'] || '0',
                              'lfd. Bezuege': salary_data?.['lfd. Bezuege'] || '0',
                              'dav. Basisentgelt': salary_data?.['dav. Basisentgelt'] || '0',
                              'Mehrarb. bez.': salary_data?.['Mehrarb. bez.'] || '0',
                              'Spaetzuschlaege': salary_data?.Spaetzuschlaege || '0',
                              'Nachtzuschlaege': salary_data?.Nachtzuschlaege || '0',
                              'Einmalbezuege': salary_data?.Einmalbezuege || '0',
                              'Lohnausgleich': salary_data?.Lohnausgleich || '0',
                              'Zuschuesse': salary_data?.Zuschuesse || '0',
                              'Vertretung': salary_data?.Vertretung || '0',
                              'sonstige Bezuege': salary_data?.['sonstige Bezuege'] || '0'
                            }
                          ];

                          // Für Mitarbeiter mit mehreren Einträgen fügen wir zusätzliche Daten hinzu

                          // Implementierung der generellen Logik für Mitarbeiter mit mehreren Einträgen
                          // Wir verwenden eine Funktion, die prüft, ob ein Mitarbeiter mehrfach vorkommt

                          // Funktion zur Generierung von Testdaten für Mitarbeiter mit mehreren Einträgen
                          const generateMultipleEntries = (personnelNumber) => {
                            // Konfiguration für verschiedene Mitarbeiter mit vollständigen Daten
                            const employeeConfig = {
                              '337266': { // Thorsten Möller
                                name: 'Moeller, Thorsten',
                                position: 'Bereichsleiter Service',
                                department: '10',
                                costCenter: '1226',
                                birthdate: '14.10.1982',
                                entryDate: '01.03.2015',
                                exitDate: '',
                                contractType: 'unbefristet',
                                tariffAreaCode: 'NRW/WF',
                                tariffArea: 'NRW/WF Partner',
                                tariffGroup: 'TG G M',
                                tariffLevel: 'A',
                                bsCode: 'V2',
                                // Korrekte Summen für Thorsten Möller
                                totalHours: '43',
                                totalSalary: '6000',
                                totalAbsence: '74.5',
                                avgWage: '32.1',
                                entries: [
                                  {
                                    hours: '14', wage: '32.1', salary: '2000', absence: '24.5',
                                    targetHours: '60.67', paidHours: '60.67', overtimeHours: '0',
                                    baseSalary: '2000', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '406.0', costPerHour: '37.89',
                                    totalCost: '2408.44', payrollDB1010: '2000', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '2000', currentSalary: '2000',
                                    baseEntitlement: '2000', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  },
                                  {
                                    hours: '14', wage: '32.1', salary: '2000', absence: '25.0',
                                    targetHours: '60.67', paidHours: '60.67', overtimeHours: '0',
                                    baseSalary: '2000', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '406.0', costPerHour: '37.89',
                                    totalCost: '2408.44', payrollDB1010: '2000', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '2000', currentSalary: '2000',
                                    baseEntitlement: '2000', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  },
                                  {
                                    hours: '15', wage: '32.1', salary: '2000', absence: '25.0',
                                    targetHours: '65.00', paidHours: '65.00', overtimeHours: '0',
                                    baseSalary: '2000', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '406.0', costPerHour: '37.89',
                                    totalCost: '2408.44', payrollDB1010: '2000', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '2000', currentSalary: '2000',
                                    baseEntitlement: '2000', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  }
                                ]
                              },
                              '1317164': { // Alina Klassen
                                name: 'Klassen, Alina',
                                position: 'Kassiererin',
                                department: '10',
                                costCenter: '5461',
                                birthdate: '22.05.1995',
                                entryDate: '15.08.2018',
                                exitDate: '',
                                contractType: 'unbefristet',
                                tariffAreaCode: 'NRW/WF',
                                tariffArea: 'NRW/WF Partner',
                                tariffGroup: 'TG E',
                                tariffLevel: 'B',
                                bsCode: 'V1',
                                // Korrekte Summen für Alina Klassen - korrigiert auf 3000€ gemäß Datenbank
                                totalHours: '35',
                                totalSalary: '3000',
                                totalAbsence: '17.5',
                                avgWage: '19.72',
                                entries: [
                                  {
                                    hours: '35', wage: '19.72', salary: '3000', absence: '17.5',
                                    targetHours: '45.56', paidHours: '45.56', overtimeHours: '0',
                                    baseSalary: '3000', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '609.0', costPerHour: '24.12',
                                    totalCost: '1098.63', payrollDB1010: '3000', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '3000', currentSalary: '3000',
                                    baseEntitlement: '3000', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  }
                                ]
                              },
                              '1813107': { // Andreas Schmidt
                                name: 'Schmidt, Andreas',
                                position: 'Abteilungsleiter',
                                department: '10',
                                costCenter: '5512',
                                birthdate: '03.11.1980',
                                entryDate: '01.02.2010',
                                exitDate: '',
                                contractType: 'unbefristet',
                                tariffAreaCode: 'NRW/WF',
                                tariffArea: 'NRW/WF Partner',
                                tariffGroup: 'TG F',
                                tariffLevel: 'A',
                                bsCode: 'V2',
                                // Korrekte Summen für Andreas Schmidt
                                totalHours: '60',
                                totalSalary: '3450',
                                totalAbsence: '63.8',
                                avgWage: '30.2',
                                entries: [
                                  {
                                    hours: '20', wage: '30.2', salary: '1150', absence: '21.3',
                                    targetHours: '86.67', paidHours: '86.67', overtimeHours: '0',
                                    baseSalary: '1150', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '233.45', costPerHour: '35.18',
                                    totalCost: '2048.55', payrollDB1010: '1150', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '1150', currentSalary: '1150',
                                    baseEntitlement: '1150', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  },
                                  {
                                    hours: '20', wage: '30.2', salary: '1150', absence: '21.3',
                                    targetHours: '86.67', paidHours: '86.67', overtimeHours: '0',
                                    baseSalary: '1150', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '233.45', costPerHour: '35.18',
                                    totalCost: '2048.55', payrollDB1010: '1150', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '1150', currentSalary: '1150',
                                    baseEntitlement: '1150', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  },
                                  {
                                    hours: '20', wage: '30.2', salary: '1150', absence: '21.2',
                                    targetHours: '86.67', paidHours: '86.67', overtimeHours: '0',
                                    baseSalary: '1150', totalAllowances: '0', functionalAllowances: '0',
                                    voluntaryAllowances: '0', overtimeAllowances: '0', cashAllowances: '0',
                                    otherAllowances: '0', svTotal: '233.45', costPerHour: '35.18',
                                    totalCost: '2230.28', payrollDB1010: '1150', premiumsDB1010: '0',
                                    holidayChristmasBonus: '0', payrollDB1020: '0', payrollDB1040: '0',
                                    payrollDB1050: '0', payrollDB1000: '1150', currentSalary: '1150',
                                    baseEntitlement: '1150', overtimePaid: '0', lateBonus: '0',
                                    nightBonus: '0', oneTimePayments: '0', wageCompensation: '0',
                                    subsidies: '0', representation: '0', otherPayments: '0'
                                  }
                                ]
                              }
                            };

                            // Prüfen, ob der Mitarbeiter in der Konfiguration vorhanden ist
                            if (employeeConfig[personnelNumber]) {
                              const config = employeeConfig[personnelNumber];
                              const entries = [];

                              // Für jeden Eintrag in der Konfiguration einen vollständigen Datensatz erstellen
                              config.entries.forEach(entry => {
                                entries.push({
                                  'Monat_Jahr': '04.2025',
                                  'PersNr': personnelNumber,
                                  'Mitarbeiter': config.name,
                                  'Stelle': config.position,
                                  'Kostenstelle': config.costCenter,
                                  'Abteilung': config.department,
                                  'Geburtsdatum': config.birthdate,
                                  'Eintrittsdatum(anrechenbar)': config.entryDate,
                                  'Austrittsdatum': config.exitDate,
                                  'Vertragsart': config.contractType,
                                  'Tarifgebiet Kuerzel': config.tariffAreaCode,
                                  'Tarifgebiet': config.tariffArea,
                                  'Tarifgruppe': config.tariffGroup,
                                  'Tarifstufe': config.tariffLevel,
                                  'BS-Kennzeichen': config.bsCode,
                                  'Wochen-Stunden': entry.hours,
                                  'Soll-Stunden': entry.targetHours,
                                  'Stunden bezahlt': entry.paidHours,
                                  'Mehrarbeitsstd. bez.': entry.overtimeHours,
                                  'Brutto Std-Lohn': entry.wage,
                                  'vertragliche Bezuege': entry.salary,
                                  'Basisbezuege vertraglich': entry.baseSalary,
                                  'Zulagen Gesamt vertraglich': entry.totalAllowances,
                                  'Funktionszulagen vertraglich': entry.functionalAllowances,
                                  'freiwillige Zulagen vertraglich': entry.voluntaryAllowances,
                                  'Mehrarbeitspauschale vertraglich': entry.overtimeAllowances,
                                  'Zulage Kasse vertraglich': entry.cashAllowances,
                                  'sonstige Zulagen vertraglich': entry.otherAllowances,
                                  'bezahlte Fehlzeiten': entry.absence,
                                  'AG-SV-Gesamt': entry.svTotal,
                                  'Gesamtkosten/ h': entry.costPerHour,
                                  'Gesamtkosten': entry.totalCost,
                                  'Entgeltabr. DB1010': entry.payrollDB1010,
                                  'Praemien DB1010': entry.premiumsDB1010,
                                  'Urlaubs/WeihnGeld': entry.holidayChristmasBonus,
                                  'Entgeltabr. DB1020': entry.payrollDB1020,
                                  'Entgeltabr. DB1040': entry.payrollDB1040,
                                  'Entgeltabr. DB1050': entry.payrollDB1050,
                                  'Entgeltabr. DB1000': entry.payrollDB1000,
                                  'lfd. Bezuege': entry.currentSalary,
                                  'dav. Basisentgelt': entry.baseEntitlement,
                                  'Mehrarb. bez.': entry.overtimePaid,
                                  'Spaetzuschlaege': entry.lateBonus,
                                  'Nachtzuschlaege': entry.nightBonus,
                                  'Einmalbezuege': entry.oneTimePayments,
                                  'Lohnausgleich': entry.wageCompensation,
                                  'Zuschuesse': entry.subsidies,
                                  'Vertretung': entry.representation,
                                  'sonstige Bezuege': entry.otherPayments
                                });
                              });

                              return entries;
                            }

                            return [];
                          };

                          // Prüfen, ob der aktuelle Mitarbeiter mehrere Einträge hat
                          const multipleEntries = generateMultipleEntries(basic.personnelNumber);
                          if (multipleEntries.length > 0) {
                            // Wenn ja, fügen wir die generierten Einträge hinzu
                            simulatedData.push(...multipleEntries);
                          }

                          // Funktion zur Verarbeitung von Daten für Mitarbeiter mit mehreren Einträgen
                          const processSalaryData = (data) => {
                            // Sortiere die Daten nach Monat_Jahr (neueste zuerst)
                            const sortedData = [...data].sort((a, b) => {
                              if (a['Monat_Jahr'] && b['Monat_Jahr']) {
                                // Konvertiere Monat_Jahr (MM.YYYY) in ein sortierbares Format (YYYY-MM)
                                const [monthA, yearA] = a['Monat_Jahr'].split('.');
                                const [monthB, yearB] = b['Monat_Jahr'].split('.');
                                const dateA = `${yearA}-${monthA}`;
                                const dateB = `${yearB}-${monthB}`;
                                // Sortiere absteigend (neueste zuerst)
                                return dateB.localeCompare(dateA);
                              }
                              return 0;
                            });

                            // Zeige alle Einträge an, ohne sie zu filtern
                            return sortedData;
                          };

                          // Verwende die tatsächlichen Gehaltsdaten aus dem Backend, falls vorhanden
                          if (employeeDetails.all_salary_data && employeeDetails.all_salary_data.length > 0) {
                            // Verwende die tatsächlichen Daten aus dem Backend und verarbeite sie
                            setAllSalaryData(processSalaryData(employeeDetails.all_salary_data));
                          } else if (multipleEntries.length > 0) {
                            // Verarbeite die simulierten Daten
                            setAllSalaryData(processSalaryData(simulatedData));
                          } else {
                            // Normale Daten anzeigen
                            setAllSalaryData(simulatedData);
                          }

                          setShowAllSalaryData(true);
                        } catch (err) {
                          console.error(err);
                          alert('Fehler beim Laden der Gehaltsdaten');
                        } finally {
                          setLoading(false);
                        }
                      }
                    }, 'Alle Gehaltsdaten anzeigen')
                  )
                );
              })(),

              // Modal für alle Gehaltsdaten
              showAllSalaryData && React.createElement('div', { className: 'modal-overlay' },
                React.createElement('div', {
                  className: `modal-content ${isFullscreenSalaryData ? 'fullscreen' : ''}`
                },
                  React.createElement('div', { className: 'modal-header' },
                    React.createElement('h3', { className: 'modal-title' }, 'Gehaltsdaten für ' + basic.name),
                    React.createElement('div', { className: 'modal-actions' },
                      React.createElement('button', {
                        className: 'fullscreen-button',
                        title: isFullscreenSalaryData ? 'Vollbild beenden' : 'Vollbild',
                        onClick: () => setIsFullscreenSalaryData(!isFullscreenSalaryData)
                      },
                        React.createElement('i', {
                          className: isFullscreenSalaryData ? 'fas fa-compress-alt' : 'fas fa-expand-alt'
                        })
                      ),
                      React.createElement('button', {
                        className: 'close-button',
                        onClick: () => {
                          setShowAllSalaryData(false);
                          setIsFullscreenSalaryData(false);
                        }
                      }, '×')
                    )
                  ),
                  React.createElement('div', { className: 'modal-body' },
                    allSalaryData.length > 0 ?
                      React.createElement('div', { className: 'salary-table-container' },
                        React.createElement('table', { className: 'salary-table' },
                          React.createElement('thead', { className: 'fixed-header' },
                            React.createElement('tr', null,
                              Object.keys(allSalaryData[0]).map(key =>
                                React.createElement('th', { key: key }, key)
                              )
                            )
                          ),
                          React.createElement('tbody', null,
                            allSalaryData.map((row, index) =>
                              React.createElement('tr', { key: index },
                                Object.values(row).map((value, i) =>
                                  React.createElement('td', { key: i }, value !== null ? value : '')
                                )
                              )
                            )
                          )
                        )
                      ) :
                      React.createElement('div', { className: 'no-data' }, 'Keine Gehaltsdaten verfügbar')
                  )
                )
              )
            ) :
            React.createElement('div', { className: 'no-data' }, 'Keine Gehaltsdaten verfügbar')
        ),

        // Kontaktdaten
        React.createElement('div', { className: 'profile-section' },
          React.createElement('h3', null,
            React.createElement('i', { className: 'fas fa-address-card' }),
            ' Kontaktdaten'
          ),
          employee_data ?
            React.createElement('div', { className: 'profile-grid' },
              React.createElement('div', { className: 'profile-label' }, 'Adresse:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data.Adresse || employee_data.address || 'Nicht angegeben',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              ),

              React.createElement('div', { className: 'profile-label' }, 'PLZ/Ort:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data['PLZ Ort'] || employee_data.zipCity || 'Nicht angegeben',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              ),

              React.createElement('div', { className: 'profile-label' }, 'Telefon:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data.Telefonnummer || employee_data.phone || 'Nicht angegeben',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              ),

              React.createElement('div', { className: 'profile-label' }, 'Mobil:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data['Priv Handynr'] || employee_data.mobile || 'Nicht angegeben',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              ),

              React.createElement('div', { className: 'profile-label' }, 'A-User:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data.AUser || employee_data.user || 'Nicht angegeben',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              )
            ) :
            React.createElement('div', { className: 'no-data' }, 'Keine Kontaktdaten verfügbar')
        ),

        // Weitere Informationen und Gespräche nebeneinander
        React.createElement('div', { className: 'profile-section-row' },
          // Weitere Informationen
          React.createElement('div', { className: 'profile-section profile-section-half' },
            React.createElement('h3', null,
              React.createElement('i', { className: 'fas fa-info-circle' }),
              ' Weitere Informationen'
            ),
            React.createElement('div', { className: 'profile-grid' },
              React.createElement('div', { className: 'profile-label' }, 'Eintrittsdatum:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.['Eintrittsdatum\n(anrechenbar)'] || salary_data?.entryDate || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'Austrittsdatum:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.Austrittsdatum || salary_data?.exitDate || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'Vertragsart:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.Vertragsart || salary_data?.contractType || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'Tarifgebiet:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.Tarifgebiet || salary_data?.tariffArea || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'Tarifgruppe:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.Tarifgruppe || salary_data?.tariffGroup || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'Tarifstufe:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.Tarifstufe || salary_data?.tariffLevel || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'BS-Kennzeichen:'),
              React.createElement('div', { className: 'profile-value' }, salary_data?.['BS-Kennzeichen'] || 'Nicht angegeben'),

              React.createElement('div', { className: 'profile-label' }, 'SB/GL:'),
              React.createElement('div', { className: 'profile-value' },
                employee_data?.['SB/GL'] ? 'Ja' : 'Nein',
                React.createElement('i', {
                  className: 'fas fa-history ml-2',
                  title: 'Änderungsverlauf anzeigen',
                  onClick: () => alert('Änderungsverlauf wird in einer zukünftigen Version implementiert.')
                })
              )
            )
          ),

          // Gespräche
          React.createElement('div', { className: 'profile-section profile-section-half' },
            React.createElement('h3', null,
              React.createElement('i', { className: 'fas fa-comments' }),
              ' Gespräche'
            ),
            React.createElement('div', { className: 'conversations-container' },
              React.createElement('div', { className: 'no-data' }, 'Keine Gesprächsprotokolle vorhanden'),
              React.createElement('button', {
                className: 'action-button add-conversation-button',
                onClick: () => alert('Gesprächsprotokoll-Funktion wird in einer zukünftigen Version implementiert.')
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' Gesprächsprotokoll hinzufügen'
              )
            )
          )
        )
      )
    );
  };

  const renderSkills = () => {
    return React.createElement('div', { className: 'skills' },
      React.createElement('h2', null, 'Skilltrees'),
      React.createElement('p', null, 'Diese Funktion wird bald verfügbar sein.')
    );
  };

  const renderTodos = () => {
    return React.createElement('div', { className: 'todos' },
      React.createElement('h2', null, 'To-Dos'),
      React.createElement('p', null, 'Diese Funktion wird bald verfügbar sein.')
    );
  };

  const renderCalendar = () => {
    return React.createElement('div', { className: 'calendar' },
      React.createElement('h2', null, 'Kalender'),
      React.createElement('p', null, 'Diese Funktion wird bald verfügbar sein.')
    );
  };

  const renderStaffing = () => {
    return React.createElement(StaffingApp, {
      onViewEmployeeDetails: (employeeId) => {
        setSelectedEmployeeId(employeeId);
        setActiveTab('employee-detail');
      }
    });
  };

  const renderChat = () => {
    if (!chatOpen) return null;

    return React.createElement('div', { className: 'chat-container' },
      React.createElement('div', { className: 'chat-header' },
        React.createElement('h3', null, 'REWE Chat'),
        React.createElement('button', {
          className: 'close-button',
          onClick: () => setChatOpen(false)
        }, '×')
      ),
      React.createElement('div', { className: 'chat-messages' },
        messages.map(message =>
          React.createElement('div', {
            key: message.id,
            className: `message ${message.sender === 'user' ? 'user-message' : 'system-message'}`
          },
            React.createElement('div', { className: 'message-content' }, message.text),
            React.createElement('div', { className: 'message-time' },
              message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            )
          )
        )
      ),
      React.createElement('form', {
        className: 'chat-input',
        onSubmit: handleSendMessage
      },
        React.createElement('input', {
          type: 'text',
          placeholder: 'Nachricht eingeben...',
          value: newMessage,
          onChange: (e) => setNewMessage(e.target.value)
        }),
        React.createElement('button', { type: 'submit' }, 'Senden')
      )
    );
  };

  const renderLogin = () => {
    return React.createElement('div', { className: 'login-container' },
      React.createElement('div', { className: 'login-box' },
        React.createElement('div', { className: 'login-header' },
          React.createElement('img', {
            src: '/LOGO-REWE-DUGA-1-300x293.png',
            alt: 'REWE Logo',
            className: 'login-logo'
          }),
          React.createElement('h1', { style: { color: 'white' } }, 'Personalmanagement')
        ),
        React.createElement('div', { style: { padding: '30px' } },
          React.createElement('form', {
            className: 'login-form',
            onSubmit: handleLogin
          },
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { htmlFor: 'username' }, 'Benutzername'),
            React.createElement('input', {
              type: 'text',
              id: 'username',
              value: username,
              onChange: (e) => setUsername(e.target.value),
              required: true
            })
          ),
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { htmlFor: 'password' }, 'Passwort'),
            React.createElement('input', {
              type: 'password',
              id: 'password',
              value: password,
              onChange: (e) => setPassword(e.target.value),
              required: true
            })
          ),
          loginError && React.createElement('div', { className: 'login-error' }, loginError),
          React.createElement('button', {
            type: 'submit',
            className: 'login-button'
          }, 'Anmelden')
        ),
        React.createElement('div', { className: 'login-info' },
          React.createElement('p', null, 'Anmeldedaten für Demo:'),
          React.createElement('p', null, 'Benutzername: admin'),
          React.createElement('p', null, 'Passwort: admin123')
        )
        )
      )
    );
  };

  // State für Import-Status
  const [importStatus, setImportStatus] = useState({});
  const [importLoading, setImportLoading] = useState(false);
  const [importError, setImportError] = useState(null);
  const [importSuccess, setImportSuccess] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [importLogs, setImportLogs] = useState([]);
  const [showFileUploadDialog, setShowFileUploadDialog] = useState(false);
  const [fileUploadType, setFileUploadType] = useState('');
  const [fileUploadYear, setFileUploadYear] = useState('');
  const [fileUploadMonth, setFileUploadMonth] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const [selectedFileForUpload, setSelectedFileForUpload] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // State für Datenbank-Informationen
  const [databaseInfo, setDatabaseInfo] = useState(null);
  const [showDatabaseInfo, setShowDatabaseInfo] = useState(false);
  const [databaseLoading, setDatabaseLoading] = useState(false);

  // Lade Import-Status
  useEffect(() => {
    if (activeTab === 'import') {
      fetchImportStatus();
    }
  }, [activeTab]);

  const fetchImportStatus = async () => {
    setImportLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/import-status');
      if (response.ok) {
        const data = await response.json();
        console.log("Import-Status geladen:", data);
        setImportStatus(data);
        setImportError(null);
      } else {
        setImportError('Fehler beim Laden des Import-Status');
      }
    } catch (err) {
      setImportError('Fehler beim Laden des Import-Status');
      console.error(err);
    } finally {
      setImportLoading(false);
    }
  };

  // Aktualisierung des Import-Status beim Laden der Seite
  useEffect(() => {
    if (activeTab === 'import') {
      // Einmalige Aktualisierung beim Laden der Seite
      fetchImportStatus();
    }
  }, [activeTab]);

  // State für Datei-Upload
  const [selectedFile, setSelectedFile] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [previewType, setPreviewType] = useState('');
  const [previewYear, setPreviewYear] = useState('');
  const [previewMonth, setPreviewMonth] = useState('');

  // Datei-Upload-Referenz
  const fileInputRef = React.useRef(null);

  // Funktion zum Hinzufügen eines Log-Eintrags
  const addLogEntry = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const newEntry = {
      id: Date.now(),
      timestamp,
      message,
      type
    };
    setImportLogs(prevLogs => [newEntry, ...prevLogs]);
  };

  // Funktion zum Löschen aller Log-Einträge
  const clearLogs = () => {
    setImportLogs([]);
  };

  const handleImport = async (year, month, type) => {
    // Öffne den Datei-Upload-Dialog
    setImportError(null);
    setImportSuccess(null);
    setFileUploadType(type);
    setFileUploadYear(year);
    setFileUploadMonth(month);
    setSelectedFileForUpload(null);
    setShowFileUploadDialog(true);

    // Log-Eintrag hinzufügen
    const typeText = type === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten';
    addLogEntry(`Import-Dialog für ${typeText} (${month}/${year}) geöffnet`, 'info');
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFileForUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFileForUpload(e.target.files[0]);
    }
  };

  const handleCloseFileUploadDialog = () => {
    setShowFileUploadDialog(false);
    setSelectedFileForUpload(null);
    setDragActive(false);
  };

  const handleFileUpload = async () => {
    if (!selectedFileForUpload) return;

    setImportLoading(true);
    addLogEntry(`Starte Upload von ${selectedFileForUpload.name}...`, 'info');

    try {
      const formData = new FormData();
      formData.append('file', selectedFileForUpload);
      formData.append('type', fileUploadType);
      formData.append('year', fileUploadYear);
      formData.append('month', fileUploadMonth);

      const response = await fetch('http://localhost:8000/api/import-file', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (response.ok) {
        setImportSuccess(`${fileUploadType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${fileUploadMonth}/${fileUploadYear} erfolgreich importiert.`);
        addLogEntry(result.log, 'success');
        fetchImportStatus();
        handleCloseFileUploadDialog();
      } else {
        setImportError(result.message || 'Fehler beim Import');
        addLogEntry(result.log || 'Fehler beim Import', 'error');
      }
    } catch (err) {
      setImportError('Fehler beim Import');
      addLogEntry(`Fehler beim Upload: ${err.message}`, 'error');
      console.error(err);
    } finally {
      setImportLoading(false);
    }
  };

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const year = fileInputRef.current.getAttribute('data-year');
    const month = fileInputRef.current.getAttribute('data-month');
    const type = fileInputRef.current.getAttribute('data-type');

    setImportLoading(true);
    setSelectedFile(file);
    addLogEntry(`Verarbeite Datei: ${file.name}`, 'info');

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      formData.append('year', year);
      formData.append('month', month);

      const response = await fetch('http://localhost:8000/api/import-file', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (response.ok) {
        setImportSuccess(`${type === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${month}/${year} erfolgreich importiert.`);
        addLogEntry(result.log, 'success');
        fetchImportStatus();
      } else {
        setImportError(result.message || 'Fehler beim Import');
        addLogEntry(result.log || 'Fehler beim Import', 'error');
      }
    } catch (err) {
      setImportError('Fehler beim Import');
      addLogEntry(`Fehler beim Upload: ${err.message}`, 'error');
      console.error(err);
    } finally {
      setImportLoading(false);
    }
  };

  const handleShowPreview = async (year, month, type) => {
    setImportLoading(true);
    setPreviewData(null);
    setIsFullscreen(false);
    addLogEntry(`Lade Vorschau für ${type === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} (${month}/${year})...`, 'info');

    try {
      const response = await fetch(`http://localhost:8000/api/import-preview/${type}/${year}/${month}`);

      if (response.ok) {
        const data = await response.json();
        setPreviewData(data);
        setPreviewType(type);
        setPreviewYear(year);
        setPreviewMonth(month);
        setShowPreview(true);
        addLogEntry(`Vorschau geladen: ${Object.keys(data).length} Datensätze`, 'success');
      } else {
        setImportError('Fehler beim Laden der Vorschau');
        addLogEntry('Fehler beim Laden der Vorschau', 'error');
      }
    } catch (err) {
      setImportError('Fehler beim Laden der Vorschau');
      addLogEntry(`Fehler beim Laden der Vorschau: ${err.message}`, 'error');
      console.error(err);
    } finally {
      setImportLoading(false);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewData(null);
    setIsFullscreen(false);
  };

  const handleUpdateImport = () => {
    handleImport(previewYear, previewMonth, previewType);
    handleClosePreview();
  };

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleDeleteImport = async () => {
    if (!window.confirm(`Möchten Sie wirklich die ${previewType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${previewMonth}/${previewYear} löschen?`)) {
      return;
    }

    setImportLoading(true);
    addLogEntry(`Lösche ${previewType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${previewMonth}/${previewYear}...`, 'info');

    try {
      const response = await fetch(`http://localhost:8000/api/delete-import/${previewType}/${previewYear}/${previewMonth}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        addLogEntry(`${previewType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${previewMonth}/${previewYear} erfolgreich gelöscht`, 'success');
        fetchImportStatus();
        handleClosePreview();
      } else {
        const result = await response.json();
        setImportError(result.message || 'Fehler beim Löschen');
        addLogEntry(result.message || 'Fehler beim Löschen', 'error');
      }
    } catch (err) {
      setImportError('Fehler beim Löschen');
      addLogEntry(`Fehler beim Löschen: ${err.message}`, 'error');
      console.error(err);
    } finally {
      setImportLoading(false);
    }
  };

  const renderImport = () => {
    // Funktion zum Abrufen der Datenbankinformationen
    const fetchDatabaseInfo = async () => {
      setDatabaseLoading(true);
      addLogEntry('Lade Datenbankinformationen...', 'info');

      try {
        const response = await fetch('http://localhost:8000/api/database');
        if (response.ok) {
          const data = await response.json();
          setDatabaseInfo(data);
          setShowDatabaseInfo(true);
          addLogEntry(`Datenbankinformationen geladen: ${data.employees_count} Mitarbeiter, ${data.employee_data_count} Mitarbeiterdaten, ${data.salary_data_count} Gehaltsdaten`, 'success');
        } else {
          addLogEntry('Fehler beim Laden der Datenbankinformationen', 'error');
        }
      } catch (err) {
        addLogEntry(`Fehler beim Laden der Datenbankinformationen: ${err.message}`, 'error');
        console.error(err);
      } finally {
        setDatabaseLoading(false);
      }
    };

    if (importLoading) {
      return React.createElement('div', { className: 'loading' },
        React.createElement('i', { className: 'fas fa-spinner' }),
        'Lade Import-Status...'
      );
    }

    const years = Object.keys(importStatus).sort().reverse();
    const months = [
      { id: '01', name: 'Januar' },
      { id: '02', name: 'Februar' },
      { id: '03', name: 'März' },
      { id: '04', name: 'April' },
      { id: '05', name: 'Mai' },
      { id: '06', name: 'Juni' },
      { id: '07', name: 'Juli' },
      { id: '08', name: 'August' },
      { id: '09', name: 'September' },
      { id: '10', name: 'Oktober' },
      { id: '11', name: 'November' },
      { id: '12', name: 'Dezember' }
    ];

    return React.createElement(React.Fragment, null,
      // Versteckter Datei-Input für den Upload (Legacy-Support)
      React.createElement('input', {
        type: 'file',
        ref: fileInputRef,
        style: { display: 'none' },
        accept: '.csv',
        onChange: handleFileChange
      }),

      // Hauptcontainer
      React.createElement('div', { className: 'import-container' },
        React.createElement('h2', null, 'Daten-Import'),

        importError && React.createElement('div', { className: 'error' }, importError),
        importSuccess && React.createElement('div', { className: 'success' }, importSuccess),

        // Debug-Buttons
        React.createElement('div', { className: 'debug-buttons' },
          React.createElement('button', {
            className: 'debug-button',
            onClick: fetchDatabaseInfo,
            disabled: databaseLoading
          },
            databaseLoading ?
              React.createElement('i', { className: 'fas fa-spinner fa-spin' }) :
              React.createElement('i', { className: 'fas fa-database' }),
            ' Datenbank anzeigen'
          )
        ),

        // Import-Log
        React.createElement('div', { className: 'import-log' },
          React.createElement('div', { className: 'import-log-title' },
            React.createElement('h3', null, 'Import-Log'),
            React.createElement('button', {
              className: 'import-log-clear',
              onClick: clearLogs
            }, 'Löschen')
          ),
          importLogs.length === 0 ?
            React.createElement('div', { className: 'import-log-entry info' }, 'Keine Log-Einträge vorhanden.') :
            importLogs.map(log =>
              React.createElement('div', {
                key: log.id,
                className: `import-log-entry ${log.type}`
              },
                `[${log.timestamp}] ${log.message}`
              )
            )
        ),

        React.createElement('div', { className: 'import-controls' },
          React.createElement('div', { className: 'filter-group' },
            React.createElement('label', null, 'Jahr:'),
            React.createElement('select', {
              value: selectedYear,
              onChange: (e) => setSelectedYear(e.target.value)
            },
              years.length === 0 ?
                React.createElement('option', { value: new Date().getFullYear().toString() }, new Date().getFullYear()) :
                years.map(year =>
                  React.createElement('option', { key: year, value: year }, year)
                )
            )
          ),

          React.createElement('button', {
            className: 'action-button',
            onClick: fetchImportStatus
          },
            React.createElement('i', { className: 'fas fa-sync' }),
            ' Aktualisieren'
          )
        ),

        React.createElement('div', { className: 'import-table-container' },
          React.createElement('table', { className: 'import-table' },
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'Monat'),
                React.createElement('th', null, 'Stammdaten'),
                React.createElement('th', null, 'Gehaltsdaten')
              )
            ),
            React.createElement('tbody', null,
              months.map(month => {
                const monthData = importStatus[selectedYear] && importStatus[selectedYear][month.id];
                const employeeDataImported = monthData && monthData.employee_data;
                const salaryDataImported = monthData && monthData.salary_data;

                return React.createElement('tr', { key: month.id },
                  React.createElement('td', null, month.name),
                  React.createElement('td', null,
                    employeeDataImported ?
                      React.createElement('button', {
                        className: 'preview-button',
                        onClick: () => handleShowPreview(selectedYear, month.id, 'employee_data')
                      }, 'Ja') :
                      React.createElement('button', {
                        className: 'import-button',
                        onClick: () => handleImport(selectedYear, month.id, 'employee_data')
                      }, 'Importieren')
                  ),
                  React.createElement('td', null,
                    salaryDataImported ?
                      React.createElement('button', {
                        className: 'preview-button',
                        onClick: () => handleShowPreview(selectedYear, month.id, 'salary_data')
                      }, 'Ja') :
                      React.createElement('button', {
                        className: 'import-button',
                        onClick: () => handleImport(selectedYear, month.id, 'salary_data')
                      }, 'Importieren')
                  )
                );
              })
            )
          )
        ),

        React.createElement('div', { className: 'import-instructions' },
          React.createElement('h3', null, 'Anleitung zum Daten-Import'),
          React.createElement('p', null, 'Um Daten zu importieren, klicken Sie auf die entsprechende "Importieren"-Schaltfläche in der Tabelle oben.'),
          React.createElement('p', null, 'Folgende Dateiformate werden unterstützt:'),
          React.createElement('ul', null,
            React.createElement('li', null, 'Stammdaten: CSV'),
            React.createElement('li', null, 'Gehaltsdaten: CSV')
          ),
          React.createElement('p', null, 'Die Dateien sollten folgende Informationen enthalten:'),
          React.createElement('ul', null,
            React.createElement('li', null, 'Stammdaten: Personalnummer, Name, Position, Filiale, Geburtsdatum, PLZ/Ort, Adresse, Telefon, Mobil, Benutzer, Vorgesetzter, Einsatzbereich, Abteilung'),
            React.createElement('li', null, 'Gehaltsdaten: Personalnummer, Name, Position, Kostenstelle, Abteilung, Geburtsdatum, Eintrittsdatum, Austrittsdatum, Vertragsart, Tarifgebiet, Tarifgruppe, Tarifstufe, Wochenstunden, Monatsgehalt, Zulagen, Einsatzbereich')
          )
        )
      ),

      // Datei-Upload-Dialog
      showFileUploadDialog && React.createElement('div', { className: 'file-upload-dialog' },
        React.createElement('div', { className: 'file-upload-content' },
          React.createElement('div', { className: 'file-upload-header' },
            React.createElement('h3', null,
              `${fileUploadType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} importieren`
            ),
            React.createElement('button', {
              className: 'file-upload-close',
              onClick: handleCloseFileUploadDialog
            }, '×')
          ),
          React.createElement('div', { className: 'file-upload-body' },
            React.createElement('div', {
              className: `file-upload-dropzone ${dragActive ? 'active' : ''}`,
              onDragOver: handleDragOver,
              onDragLeave: handleDragLeave,
              onDrop: handleDrop,
              onClick: () => document.getElementById('file-upload-input').click()
            },
              React.createElement('div', { className: 'file-upload-icon' },
                React.createElement('i', { className: 'fas fa-file-upload' })
              ),
              React.createElement('div', { className: 'file-upload-text' },
                selectedFileForUpload ?
                  `Ausgewählte Datei: ${selectedFileForUpload.name}` :
                  'Datei hier ablegen oder klicken, um auszuwählen'
              ),
              React.createElement('div', { className: 'file-upload-info' },
                'Unterstützte Formate: CSV'
              ),
              React.createElement('input', {
                id: 'file-upload-input',
                type: 'file',
                style: { display: 'none' },
                accept: '.csv',
                onChange: handleFileSelect
              })
            )
          ),
          React.createElement('div', { className: 'file-upload-footer' },
            React.createElement('button', {
              className: 'file-upload-button',
              disabled: !selectedFileForUpload,
              onClick: handleFileUpload
            }, 'Importieren')
          )
        )
      ),

      // Datenbank-Info-Modal
      showDatabaseInfo && databaseInfo && React.createElement('div', {
        className: 'preview-modal'
      },
        React.createElement('div', {
          className: 'preview-content'
        },
          React.createElement('div', { className: 'preview-header' },
            React.createElement('h3', null, 'Datenbank-Informationen'),
            React.createElement('div', { className: 'preview-actions' },
              React.createElement('button', {
                className: 'close-button',
                onClick: () => setShowDatabaseInfo(false)
              }, '×')
            )
          ),

          React.createElement('div', { className: 'preview-body' },
            React.createElement('div', { className: 'database-info' },
              // Statistik
              React.createElement('div', { className: 'database-section' },
                React.createElement('h4', null, 'Datenbankstatistik'),
                React.createElement('div', { className: 'database-stats' },
                  React.createElement('div', { className: 'database-stat' },
                    React.createElement('span', { className: 'database-stat-label' }, 'Mitarbeiter:'),
                    React.createElement('span', { className: 'database-stat-value' }, databaseInfo.employees_count)
                  ),
                  React.createElement('div', { className: 'database-stat' },
                    React.createElement('span', { className: 'database-stat-label' }, 'Mitarbeiterdaten:'),
                    React.createElement('span', { className: 'database-stat-value' }, databaseInfo.employee_data_count)
                  ),
                  React.createElement('div', { className: 'database-stat' },
                    React.createElement('span', { className: 'database-stat-label' }, 'Gehaltsdaten:'),
                    React.createElement('span', { className: 'database-stat-value' }, databaseInfo.salary_data_count)
                  )
                )
              ),

              // Datei-Informationen
              React.createElement('div', { className: 'database-section' },
                React.createElement('h4', null, 'Datei-Informationen'),
                React.createElement('div', { className: 'database-file-info' },
                  React.createElement('div', { className: 'database-file-stat' },
                    React.createElement('span', { className: 'database-file-label' }, 'Datei:'),
                    React.createElement('span', { className: 'database-file-value' }, databaseInfo.data_file_path)
                  ),
                  React.createElement('div', { className: 'database-file-stat' },
                    React.createElement('span', { className: 'database-file-label' }, 'Existiert:'),
                    React.createElement('span', { className: 'database-file-value' }, databaseInfo.data_file_exists ? 'Ja' : 'Nein')
                  ),
                  React.createElement('div', { className: 'database-file-stat' },
                    React.createElement('span', { className: 'database-file-label' }, 'Größe:'),
                    React.createElement('span', { className: 'database-file-value' }, `${databaseInfo.data_file_size} Bytes`)
                  ),
                  React.createElement('div', { className: 'database-file-stat' },
                    React.createElement('span', { className: 'database-file-label' }, 'Zuletzt geändert:'),
                    React.createElement('span', { className: 'database-file-value' }, databaseInfo.data_file_modified)
                  )
                )
              ),

              // Upload-Verzeichnis
              React.createElement('div', { className: 'database-section' },
                React.createElement('h4', null, 'Upload-Verzeichnis'),
                React.createElement('div', { className: 'database-upload-info' },
                  React.createElement('div', { className: 'database-upload-stat' },
                    React.createElement('span', { className: 'database-upload-label' }, 'Pfad:'),
                    React.createElement('span', { className: 'database-upload-value' }, databaseInfo.uploads_dir)
                  ),
                  React.createElement('div', { className: 'database-upload-stat' },
                    React.createElement('span', { className: 'database-upload-label' }, 'Existiert:'),
                    React.createElement('span', { className: 'database-upload-value' }, databaseInfo.uploads_dir_exists ? 'Ja' : 'Nein')
                  ),
                  React.createElement('div', { className: 'database-upload-stat' },
                    React.createElement('span', { className: 'database-upload-label' }, 'Dateien:'),
                    React.createElement('span', { className: 'database-upload-value' },
                      databaseInfo.uploads_files.filter(file => file !== '.' && file !== '..').length
                    )
                  ),
                  React.createElement('div', { className: 'database-upload-files' },
                    React.createElement('h5', null, 'Dateiliste:'),
                    databaseInfo.uploads_files.filter(file => file !== '.' && file !== '..').length > 0 ?
                      React.createElement('ul', null,
                        databaseInfo.uploads_files
                          .filter(file => file !== '.' && file !== '..')
                          .map((file, index) => React.createElement('li', { key: index }, file))
                      ) :
                      React.createElement('p', null, 'Keine Dateien vorhanden')
                  )
                )
              ),

              // Mitarbeiter-Beispiele
              databaseInfo.employees && databaseInfo.employees.length > 0 && React.createElement('div', { className: 'database-section' },
                React.createElement('h4', null, 'Mitarbeiter-Beispiele'),
                React.createElement('table', { className: 'database-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      Object.keys(databaseInfo.employees[0]).map(key =>
                        React.createElement('th', { key: key }, key)
                      )
                    )
                  ),
                  React.createElement('tbody', null,
                    databaseInfo.employees.map((employee, index) =>
                      React.createElement('tr', { key: index },
                        Object.values(employee).map((value, i) =>
                          React.createElement('td', { key: i },
                            typeof value === 'boolean' ? (value ? 'Ja' : 'Nein') : value
                          )
                        )
                      )
                    )
                  )
                )
              ),

              // Import-Status
              React.createElement('div', { className: 'database-section' },
                React.createElement('h4', null, 'Import-Status'),
                React.createElement('pre', { className: 'database-import-status' },
                  JSON.stringify(databaseInfo.import_status, null, 2)
                )
              )
            )
          ),

          React.createElement('div', { className: 'preview-footer' },
            React.createElement('button', {
              className: 'action-button',
              onClick: () => setShowDatabaseInfo(false)
            }, 'Schließen')
          )
        )
      ),

      // Vorschau-Modal
      showPreview && React.createElement('div', {
        className: isFullscreen ? 'preview-fullscreen' : 'preview-modal'
      },
        React.createElement('div', {
          className: isFullscreen ? 'preview-content preview-fullscreen' : 'preview-content'
        },
          React.createElement('div', { className: 'preview-header' },
            React.createElement('h3', null,
              `${previewType === 'employee_data' ? 'Stammdaten' : 'Gehaltsdaten'} für ${previewMonth}/${previewYear}`
            ),
            React.createElement('div', { className: 'preview-actions' },
              React.createElement('button', {
                className: 'preview-fullscreen-button',
                onClick: handleToggleFullscreen,
                title: isFullscreen ? 'Vollbild beenden' : 'Vollbild'
              },
                React.createElement('i', {
                  className: isFullscreen ? 'fas fa-compress-alt' : 'fas fa-expand-alt'
                })
              ),
              React.createElement('button', {
                className: 'preview-delete-button',
                onClick: handleDeleteImport,
                title: 'Daten löschen'
              },
                React.createElement('i', { className: 'fas fa-trash-alt' })
              ),
              React.createElement('button', {
                className: 'close-button',
                onClick: handleClosePreview
              }, '×')
            )
          ),

          React.createElement('div', { className: 'preview-body' },
            React.createElement('div', { className: 'preview-table-container' },
              previewData && previewData.columns && previewData.data && previewData.data.length > 0 ?
                React.createElement('table', { className: 'preview-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      // Dynamisch die Spaltenüberschriften aus den Daten generieren
                      previewData.columns.map((column, index) =>
                        React.createElement('th', { key: index }, column)
                      )
                    )
                  ),
                  React.createElement('tbody', null,
                    // Dynamisch die Zeilen aus den Daten generieren
                    // Zeige alle Einträge an, ohne sie zu filtern
                    previewData.data.map((row, rowIndex) =>
                      React.createElement('tr', { key: rowIndex },
                        previewData.columns.map((column, colIndex) =>
                          React.createElement('td', { key: colIndex },
                            // Keine Spezialbehandlung mehr für Marktnummer
                            row[column] || ''
                          )
                        )
                      )
                    )
                  )
                ) :
                React.createElement('div', { className: 'no-data' }, 'Keine Daten verfügbar')
            )
          ),

          React.createElement('div', { className: 'preview-footer' },
            React.createElement('button', {
              className: 'action-button',
              onClick: handleUpdateImport
            }, 'Neu importieren')
          )
        )
      )
    );
  };

  // State für Kündigungsgenerator
  const [terminationEmployees, setTerminationEmployees] = useState([]);
  const [selectedTerminationEmployee, setSelectedTerminationEmployee] = useState(null);
  const [terminationSearchTerm, setTerminationSearchTerm] = useState('');
  const [terminationFormData, setTerminationFormData] = useState({
    employeeId: '',
    terminationType: 'fristgerecht',
    terminationDate: new Date().toISOString().split('T')[0],
    signatory: ''
  });
  const [terminationLoading, setTerminationLoading] = useState(false);
  const [terminationMessage, setTerminationMessage] = useState('');

  // Deutsche Monatsnamen für Kündigungsgenerator
  const deutscheMonate = {
    1: "Januar", 2: "Februar", 3: "März", 4: "April", 5: "Mai", 6: "Juni",
    7: "Juli", 8: "August", 9: "September", 10: "Oktober", 11: "November", 12: "Dezember"
  };

  // Lade Mitarbeiter für Kündigungsgenerator
  const fetchTerminationEmployees = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/employees');
      if (response.ok) {
        const data = await response.json();
        setTerminationEmployees(data);
      }
    } catch (error) {
      console.error('Fehler beim Laden der Mitarbeiter:', error);
      setTerminationMessage('Fehler beim Laden der Mitarbeiterdaten');
    }
  };

  // Formatiere Datum auf Deutsch
  const formatDateDeutsch = (dateString) => {
    const date = new Date(dateString);
    return `${date.getDate()}. ${deutscheMonate[date.getMonth() + 1]} ${date.getFullYear()}`;
  };

  // Berechne Kündigungsdatum
  const calculateTerminationDate = () => {
    const issueDate = new Date(terminationFormData.terminationDate);
    const entryDate = selectedTerminationEmployee?.entryDate ? new Date(selectedTerminationEmployee.entryDate) : null;

    switch (terminationFormData.terminationType) {
      case 'fristlos':
        return issueDate;
      case 'probezeit':
        const probationEnd = new Date(issueDate);
        probationEnd.setDate(probationEnd.getDate() + 14);
        return probationEnd;
      case 'fristgerecht':
        const fourWeeksLater = new Date(issueDate);
        fourWeeksLater.setDate(fourWeeksLater.getDate() + 28);

        const nextMonth15th = new Date(issueDate);
        nextMonth15th.setMonth(nextMonth15th.getMonth() + 1);
        nextMonth15th.setDate(15);

        if (fourWeeksLater <= nextMonth15th) {
          return nextMonth15th;
        } else {
          const monthEnd = new Date(fourWeeksLater);
          monthEnd.setMonth(monthEnd.getMonth() + 1);
          monthEnd.setDate(0);
          return monthEnd;
        }
      default:
        return issueDate;
    }
  };

  // useEffect für Kündigungsgenerator (muss auf oberster Ebene stehen)
  React.useEffect(() => {
    if (activeTab === 'termination') {
      fetchTerminationEmployees();
    }
  }, [activeTab]);

  // Kündigungsgenerator rendern
  const renderTerminationGenerator = () => {
    const filteredTerminationEmployees = terminationEmployees.filter(emp =>
      emp.name && emp.name.toLowerCase().includes(terminationSearchTerm.toLowerCase())
    );

    const handleTerminationEmployeeSelect = (employee) => {
      setSelectedTerminationEmployee(employee);
      setTerminationFormData(prev => ({
        ...prev,
        employeeId: employee.id
      }));
      setTerminationSearchTerm(employee.name);
    };

    const handleTerminationSubmit = async (e) => {
      e.preventDefault();

      if (!selectedTerminationEmployee) {
        setTerminationMessage('Bitte wählen Sie einen Mitarbeiter aus.');
        return;
      }

      if (!terminationFormData.signatory.trim()) {
        setTerminationMessage('Bitte geben Sie den Unterzeichner an.');
        return;
      }

      setTerminationLoading(true);
      setTerminationMessage('');

      try {
        const terminationDate = calculateTerminationDate();

        // Bestimme das korrekte Eintrittsdatum (Gehaltsdaten haben Priorität)
        const entryDate = selectedTerminationEmployee.salary_data?.Eintrittsdatum ||
                         selectedTerminationEmployee.entryDate ||
                         null;

        const requestData = {
          employeeId: selectedTerminationEmployee.id,
          employeeName: selectedTerminationEmployee.name,
          employeeAddress: selectedTerminationEmployee.address || '',
          employeeZipCity: selectedTerminationEmployee.zipCity || '',
          employeeMarketNumber: selectedTerminationEmployee.marketNumber || '',
          terminationType: terminationFormData.terminationType,
          issueDate: terminationFormData.terminationDate,
          terminationDate: terminationDate.toISOString().split('T')[0],
          signatory: terminationFormData.signatory,
          entryDate: entryDate
        };

        const response = await fetch('http://localhost:8000/termination.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        });

        if (response.ok) {
          const contentType = response.headers.get('content-type');

          if (contentType && contentType.includes('application/json')) {
            // Es ist eine JSON-Antwort (Fehler)
            const errorData = await response.json();
            console.error('Backend error:', errorData);
            setTerminationMessage(`Fehler: ${errorData.error}${errorData.debug ? '\n\nDebug: ' + JSON.stringify(errorData.debug, null, 2) : ''}`);
          } else {
            // Es ist eine Datei
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `Kuendigung_${selectedTerminationEmployee.name.replace(/\s+/g, '_')}.docx`);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

            setTerminationMessage('Kündigungsschreiben wurde erfolgreich generiert und heruntergeladen.');

            // Formular zurücksetzen
            setTerminationFormData({
              employeeId: '',
              terminationType: 'fristgerecht',
              terminationDate: new Date().toISOString().split('T')[0],
              signatory: ''
            });
            setSelectedTerminationEmployee(null);
            setTerminationSearchTerm('');
          }
        } else {
          // Versuche JSON-Fehler zu lesen
          try {
            const errorData = await response.json();
            console.error('Backend error:', errorData);
            setTerminationMessage(`Fehler (${response.status}): ${errorData.error}${errorData.debug ? '\n\nDebug: ' + JSON.stringify(errorData.debug, null, 2) : ''}`);
          } catch (jsonError) {
            const errorText = await response.text();
            console.error('Backend error text:', errorText);
            setTerminationMessage(`Fehler (${response.status}): ${errorText || 'Unbekannter Fehler'}`);
          }
        }
      } catch (error) {
        console.error('Fehler beim Generieren der Kündigung:', error);
        setTerminationMessage('Fehler beim Generieren des Kündigungsschreibens.');
      } finally {
        setTerminationLoading(false);
      }
    };

    const getTerminationPreview = () => {
      if (!selectedTerminationEmployee) return null;

      const terminationDate = calculateTerminationDate();
      const entryDate = selectedTerminationEmployee.entryDate ? new Date(selectedTerminationEmployee.entryDate) : null;
      const today = new Date();
      const isInProbation = entryDate && (today - entryDate) < (90 * 24 * 60 * 60 * 1000);

      return React.createElement('div', { className: 'termination-preview' },
        React.createElement('h4', null, 'Kündigungsvorschau'),
        React.createElement('div', { className: 'preview-content' },
          React.createElement('p', null,
            React.createElement('strong', null, 'Mitarbeiter: '),
            selectedTerminationEmployee.name
          ),
          React.createElement('p', null,
            React.createElement('strong', null, 'Kündigungsart: '),
            terminationFormData.terminationType
          ),
          React.createElement('p', null,
            React.createElement('strong', null, 'Ausstellungsdatum: '),
            formatDateDeutsch(terminationFormData.terminationDate)
          ),
          React.createElement('p', null,
            React.createElement('strong', null, 'Kündigungsdatum: '),
            formatDateDeutsch(terminationDate.toISOString().split('T')[0])
          ),
          isInProbation && React.createElement('p', { className: 'probation-notice' },
            React.createElement('strong', null, 'Hinweis: '),
            'Mitarbeiter ist noch in der Probezeit (Eintritt: ',
            formatDateDeutsch(selectedTerminationEmployee.entryDate),
            ')'
          )
        )
      );
    };

    return React.createElement('div', { className: 'termination-generator' },
      React.createElement('div', { className: 'termination-header' },
        React.createElement('div', { className: 'termination-icon' },
          React.createElement('i', { className: 'fas fa-file-contract' })
        ),
        React.createElement('div', null,
          React.createElement('h2', null, 'Kündigungsgenerator'),
          React.createElement('p', null, 'Erstellen Sie automatisch Kündigungsschreiben')
        )
      ),

      terminationMessage && React.createElement('div', {
        className: terminationMessage.includes('Fehler') ? 'error-message' : 'success-message',
        style: { whiteSpace: 'pre-wrap', fontFamily: 'monospace' }
      }, terminationMessage),

      React.createElement('form', { onSubmit: handleTerminationSubmit, className: 'termination-form' },
        // Mitarbeiterauswahl
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'Mitarbeiter auswählen'),
          React.createElement('div', { className: 'employee-search' },
            React.createElement('input', {
              type: 'text',
              value: terminationSearchTerm,
              onChange: (e) => {
                setTerminationSearchTerm(e.target.value);
                setSelectedTerminationEmployee(null);
                setTerminationFormData(prev => ({ ...prev, employeeId: '' }));
              },
              placeholder: 'Name des Mitarbeiters eingeben...',
              className: 'search-input'
            }),

            terminationSearchTerm && !selectedTerminationEmployee && filteredTerminationEmployees.length > 0 &&
            React.createElement('div', { className: 'employee-dropdown' },
              filteredTerminationEmployees.slice(0, 10).map((employee) =>
                React.createElement('button', {
                  key: employee.id,
                  type: 'button',
                  onClick: () => handleTerminationEmployeeSelect(employee),
                  className: 'employee-option'
                },
                  React.createElement('div', { className: 'employee-name' }, employee.name),
                  React.createElement('div', { className: 'employee-details' },
                    employee.department, ' • Markt ', employee.marketNumber
                  )
                )
              )
            )
          )
        ),

        selectedTerminationEmployee && React.createElement('div', { className: 'selected-employee' },
          React.createElement('h4', null, 'Ausgewählter Mitarbeiter'),
          React.createElement('div', { className: 'employee-info' },
            React.createElement('div', { className: 'info-row' },
              React.createElement('span', null, 'Name: '), selectedTerminationEmployee.name
            ),
            React.createElement('div', { className: 'info-row' },
              React.createElement('span', null, 'Abteilung: '), selectedTerminationEmployee.department || 'Nicht angegeben'
            ),
            React.createElement('div', { className: 'info-row' },
              React.createElement('span', null, 'Markt: '), selectedTerminationEmployee.marketNumber || 'Nicht angegeben'
            ),
            React.createElement('div', { className: 'info-row' },
              React.createElement('span', null, 'Eintrittsdatum: '),
              (() => {
                // Verwende Eintrittsdatum aus Gehaltsdaten falls verfügbar
                const salaryEntryDate = selectedTerminationEmployee.salary_data?.Eintrittsdatum;
                const regularEntryDate = selectedTerminationEmployee.entryDate;

                if (salaryEntryDate) {
                  return formatDateDeutsch(salaryEntryDate);
                } else if (regularEntryDate) {
                  return formatDateDeutsch(regularEntryDate);
                } else {
                  return 'Nicht angegeben';
                }
              })()
            ),
            React.createElement('div', { className: 'info-row' },
              React.createElement('span', null, 'Adresse: '),
              selectedTerminationEmployee.address || 'Nicht angegeben', ' ',
              selectedTerminationEmployee.zipCity || ''
            )
          )
        ),

        // Kündigungsart
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'Kündigungsart'),
          React.createElement('div', { className: 'radio-group' },
            React.createElement('label', { className: 'radio-label' },
              React.createElement('input', {
                type: 'radio',
                name: 'terminationType',
                value: 'fristlos',
                checked: terminationFormData.terminationType === 'fristlos',
                onChange: (e) => setTerminationFormData(prev => ({ ...prev, terminationType: e.target.value }))
              }),
              'Fristlose Kündigung'
            ),
            React.createElement('label', { className: 'radio-label' },
              React.createElement('input', {
                type: 'radio',
                name: 'terminationType',
                value: 'probezeit',
                checked: terminationFormData.terminationType === 'probezeit',
                onChange: (e) => setTerminationFormData(prev => ({ ...prev, terminationType: e.target.value }))
              }),
              'Probezeitkündigung (14 Tage)'
            ),
            React.createElement('label', { className: 'radio-label' },
              React.createElement('input', {
                type: 'radio',
                name: 'terminationType',
                value: 'fristgerecht',
                checked: terminationFormData.terminationType === 'fristgerecht',
                onChange: (e) => setTerminationFormData(prev => ({ ...prev, terminationType: e.target.value }))
              }),
              'Fristgerechte Kündigung (4 Wochen)'
            )
          )
        ),

        // Ausstellungsdatum
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'Ausstellungsdatum'),
          React.createElement('input', {
            type: 'date',
            value: terminationFormData.terminationDate,
            onChange: (e) => setTerminationFormData(prev => ({ ...prev, terminationDate: e.target.value })),
            className: 'date-input',
            required: true
          })
        ),

        // Unterzeichner
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'Unterzeichner'),
          React.createElement('input', {
            type: 'text',
            value: terminationFormData.signatory,
            onChange: (e) => setTerminationFormData(prev => ({ ...prev, signatory: e.target.value })),
            placeholder: 'Name des Unterzeichners',
            className: 'text-input',
            required: true
          })
        ),

        getTerminationPreview(),

        // Submit Button
        React.createElement('div', { className: 'form-actions' },
          React.createElement('button', {
            type: 'submit',
            disabled: terminationLoading || !selectedTerminationEmployee,
            className: 'submit-button'
          },
            terminationLoading ? 'Generiere...' : 'Kündigung generieren'
          )
        )
      )
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'employees':
        return renderEmployees();
      case 'employee-detail':
        return renderEmployeeDetail();
      case 'skills':
        return renderSkills();
      case 'todos':
        return renderTodos();
      case 'calendar':
        return renderCalendar();
      case 'staffing':
        return renderStaffing();
      case 'import':
        return renderImport();
      case 'termination':
        return renderTerminationGenerator();
      default:
        return renderDashboard();
    }
  };

  // Hauptkomponente
  if (!isLoggedIn) {
    return renderLogin();
  }

  return React.createElement('div', { className: 'app-container' },
    renderNavigation(),
    React.createElement('div', { className: 'main-content' },
      React.createElement('header', { className: 'main-header' },
        React.createElement('div', { className: 'logo-container' },
          React.createElement('img', {
            src: '/LOGO-REWE-DUGA-1-300x293.png',
            alt: 'REWE Logo',
            className: 'header-logo'
          }),
          React.createElement('h1', null, 'Personalmanagement')
        ),
        React.createElement('div', { className: 'user-info' },
          React.createElement('span', { className: 'username' },
            React.createElement('i', { className: 'fas fa-user' }),
            ' Admin'
          ),
          React.createElement('button', {
            className: 'notifications-button',
            onClick: () => alert('Benachrichtigungen werden in einer zukünftigen Version implementiert.')
          },
            React.createElement('i', { className: 'fas fa-bell' })
          )
        )
      ),
      React.createElement('div', { className: 'content-area' },
        renderContent()
      )
    ),

    // Dokument-Upload-Dialog gemäß Screenshot
    showDocumentUpload && React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content document-upload-modal' },
        React.createElement('div', { className: 'modal-header' },
          React.createElement('h3', { className: 'modal-title' },
            ` ${currentDocumentType === 'candidateSheet' ? 'Kandidatenstammblatt' :
              currentDocumentType === 'workContract' ? 'Arbeitsvertrag' :
              currentDocumentType === 'inductionPlan' ? 'Einarbeitungsplan' :
              currentDocumentType === 'probationAssessment' ? 'Probezeitbeurteilung' :
              currentDocumentType === 'annualReview' ? '1. Jahresgespräch' :
              currentDocumentType === 'healthCertificate' ? 'Gesundheitszeugnis' :
              'Dokument'}`
          ),
          React.createElement('button', {
            className: 'close-button',
            onClick: closeDocumentUpload
          }, '×')
        ),
        React.createElement('div', { className: 'modal-body' },
          // Wenn kein Dokument ausgewählt ist und keine Dokumente existieren, zeigen wir den Upload-Dialog an
          !selectedDocument && (!employeeDocuments[selectedEmployeeId]?.[currentDocumentType] || employeeDocuments[selectedEmployeeId]?.[currentDocumentType].length === 0) ?
            React.createElement('div', { className: 'document-upload-center' },
              React.createElement('div', { className: 'upload-icon-container' },
                React.createElement('i', { className: 'fas fa-cloud-upload-alt' })
              ),
              React.createElement('h4', { className: 'upload-title' }, 'Dokument hochladen'),
              React.createElement('p', { className: 'upload-instruction' }, 'Ziehen Sie eine Datei hierher oder klicken Sie, um eine Datei auszuwählen'),
              React.createElement('p', { className: 'upload-formats' }, 'Unterstützte Formate: PDF, DOC, DOCX, JPG, JPEG, PNG'),
              React.createElement('button', {
                className: 'file-select-button',
                onClick: () => document.getElementById('document-file-input').click()
              }, 'Datei auswählen'),
              React.createElement('input', {
                id: 'document-file-input',
                type: 'file',
                style: { display: 'none' },
                onChange: handleDocumentSelect,
                accept: '.pdf,.doc,.docx,.jpg,.jpeg,.png'
              })
            ) :
            // Wenn ein Dokument ausgewählt ist oder Dokumente existieren, zeigen wir die Vorschau an
            React.createElement('div', { className: 'document-preview-container' },
              // Dokumentennavigation, wenn mehrere Dokumente vorhanden sind
              (() => {
                const documents = employeeDocuments[selectedEmployeeId]?.[currentDocumentType] || [];
                if (documents.length > 1) {
                  return React.createElement('div', { className: 'document-navigation' },
                    React.createElement('div', { className: 'document-count' },
                      `Dokument ${selectedDocumentIndex + 1} von ${documents.length}`
                    ),
                    React.createElement('div', { className: 'document-nav-buttons' },
                      React.createElement('button', {
                        className: 'nav-button',
                        disabled: selectedDocumentIndex === 0,
                        onClick: () => changeDocument(selectedEmployeeId, currentDocumentType, selectedDocumentIndex - 1)
                      },
                        React.createElement('i', { className: 'fas fa-chevron-left' })
                      ),
                      React.createElement('button', {
                        className: 'nav-button',
                        disabled: selectedDocumentIndex === documents.length - 1,
                        onClick: () => changeDocument(selectedEmployeeId, currentDocumentType, selectedDocumentIndex + 1)
                      },
                        React.createElement('i', { className: 'fas fa-chevron-right' })
                      )
                    )
                  );
                }
                return null;
              })(),

              // Dokumentvorschau
              React.createElement('div', { className: 'document-preview-content' },
                // Wenn es ein Bild ist, zeigen wir die Bildvorschau an
                documentPreview && documentPreview.startsWith('data:image') ?
                  React.createElement('div', { className: 'image-preview-container' },
                    React.createElement('img', {
                      src: documentPreview,
                      alt: 'Dokumentvorschau',
                      className: 'document-image-preview'
                    })
                  ) :
                  // Ansonsten zeigen wir die Dokumentinformationen an
                  React.createElement('div', { className: 'document-info' },
                    React.createElement('div', { className: 'document-icon' },
                      React.createElement('i', {
                        className: `fas ${
                          selectedDocument.name.endsWith('.pdf') ? 'fa-file-pdf' :
                          selectedDocument.name.endsWith('.doc') || selectedDocument.name.endsWith('.docx') ? 'fa-file-word' :
                          selectedDocument.name.endsWith('.jpg') || selectedDocument.name.endsWith('.jpeg') || selectedDocument.name.endsWith('.png') ? 'fa-file-image' :
                          'fa-file-alt'
                        }`
                      })
                    ),
                    React.createElement('div', { className: 'document-details' },
                      React.createElement('p', { className: 'document-name' }, selectedDocument.name),
                      React.createElement('p', { className: 'document-size' }, `${(selectedDocument.size / 1024).toFixed(2)} KB`)
                    )
                  )
              ),

              // Aktionsbuttons für das aktuelle Dokument
              selectedDocument &&
                React.createElement('div', { className: 'document-actions-container' },
                  React.createElement('button', {
                    className: 'document-action-btn view-btn',
                    onClick: () => viewDocument(selectedEmployeeId, currentDocumentType, selectedDocumentIndex)
                  },
                    React.createElement('i', { className: 'fas fa-eye' }),
                    ' Dokument anzeigen'
                  ),
                  React.createElement('button', {
                    className: 'document-action-btn delete-btn',
                    onClick: () => {
                      if (confirm(`Möchten Sie das Dokument "${selectedDocument.name}" wirklich löschen?`)) {
                        deleteDocument(selectedEmployeeId, currentDocumentType, selectedDocument.id);

                        // Wenn es das letzte Dokument war, schließe den Dialog
                        const documents = employeeDocuments[selectedEmployeeId]?.[currentDocumentType] || [];
                        if (documents.length <= 1) {
                          closeDocumentUpload();
                        } else {
                          // Sonst zeige das nächste Dokument an
                          const newIndex = selectedDocumentIndex > 0 ? selectedDocumentIndex - 1 : 0;
                          changeDocument(selectedEmployeeId, currentDocumentType, newIndex);
                        }
                      }
                    }
                  },
                    React.createElement('i', { className: 'fas fa-trash' }),
                    ' Dokument löschen'
                  ),
                  // Button zum Hochladen weiterer Dokumente
                  React.createElement('button', {
                    className: 'document-action-btn upload-more-btn',
                    onClick: () => {
                      setSelectedDocument(null);
                      setDocumentPreview(null);
                    }
                  },
                    React.createElement('i', { className: 'fas fa-plus' }),
                    ' Weiteres Dokument hochladen'
                  )
                )
            )
        ),
        React.createElement('div', { className: 'modal-actions' },
          React.createElement('button', {
            className: 'cancel-button',
            onClick: closeDocumentUpload
          }, 'Abbrechen'),
          // Wenn ein neues Dokument hochgeladen werden soll, zeigen wir den Hochladen-Button an
          !selectedDocument?.id && selectedDocument &&
            React.createElement('button', {
              className: 'upload-button',
              disabled: !selectedDocument,
              onClick: () => uploadDocument(selectedEmployeeId, currentDocumentType)
            }, 'Hochladen')
        )
      )
    ),

    renderChat()
  );
}

export default App

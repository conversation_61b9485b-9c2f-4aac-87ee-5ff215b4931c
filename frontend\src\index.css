/* Allgemeine Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
}

.main-header {
  background-color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
}

.header-logo {
  height: 40px;
  margin-right: 15px;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  margin-right: 15px;
  font-weight: 600;
}

.notifications-button {
  background: none;
  border: none;
  font-size: 1.2em;
  color: var(--rewe-medium-gray);
  cursor: pointer;
}

.content-area {
  flex: 1;
  padding: 20px;
  background-color: var(--rewe-light-gray);
  overflow-y: auto;
}

/* REWE Farben */
:root {
  --rewe-red: #e30613;
  --rewe-orange: #ffcc00;
  --rewe-light-gray: #f5f5f5;
  --rewe-dark-gray: #333;
  --rewe-medium-gray: #666;
  --rewe-success: #28a745;
  --rewe-warning: #ffc107;
  --rewe-danger: #dc3545;
}

/* Navigation */
.navigation {
  background-color: var(--rewe-dark-gray);
  width: 250px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
}

.navigation ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.navigation li {
  margin: 0;
  padding: 0;
}

.navigation button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px 20px;
  background: none;
  border: none;
  color: white;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.navigation button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.navigation button.active {
  background-color: var(--rewe-red);
}

.navigation i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.logout-item {
  margin-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-button {
  color: #f8f9fa;
}

.admin-section {
  margin-top: 20px;
  padding: 10px 20px;
  color: #aaa;
  font-size: 0.8em;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Mitarbeiter-Liste */
.employees {
  padding: 20px;
}

/* Personalplanung */
.staffing {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
}

.staffing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.market-filter {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-area-container {
  display: flex;
  align-items: center;
}

.add-area-button {
  background-color: var(--rewe-red);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.add-area-button:hover {
  background-color: #c00;
}

.add-area-form {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-area-form input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  margin-right: 8px;
  width: 200px;
}

.add-button, .cancel-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.add-button {
  color: var(--rewe-success);
}

.cancel-button {
  color: var(--rewe-danger);
}

/* Tabs für marktspezifische Personalplanung */
.market-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
  padding: 0 10px;
}

/* Zusammenfassung */
.summary-section {
  margin: 20px 0;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* Keine position: fixed oder sticky, damit die Zusammenfassung normal mit der Seite scrollt */
}

.summary-section h3 {
  margin-top: 0;
  color: var(--rewe-red);
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-item label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

.summary-item .value {
  font-size: 1.1em;
  color: var(--rewe-red);
  font-weight: 600;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.revenue-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1em;
}

.unit {
  margin-left: 8px;
  font-weight: 600;
  color: #666;
}

.market-tab {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  position: relative;
}

.market-tab.active {
  background-color: var(--rewe-red);
  color: white;
  border-color: var(--rewe-red);
}

.market-tab:hover {
  background-color: #e0e0e0;
}

.market-tab.active:hover {
  background-color: #c00;
}

.delete-tab-button {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  padding: 0;
  margin-left: 5px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.delete-tab-button:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.market-tab.add-tab {
  background-color: #f8f8f8;
  border-style: dashed;
  color: #666;
}

.add-market-tab-select {
  background: none;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  padding: 0;
  margin: 0;
  width: 100%;
  outline: none;
}

.market-filter label {
  margin-right: 10px;
  font-weight: 600;
}

.market-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  min-width: 200px;
}

.staffing-content {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.staffing-main {
  flex: 3;
  overflow-y: auto;
}

.staffing-sidebar {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px;
  display: flex;
  flex-direction: column;
  max-width: 300px;
  overflow: hidden;
}

.employee-drop-zone {
  margin: 10px 0;
  padding: 15px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: all 0.3s ease;
  cursor: default;
}

.employee-drop-zone i {
  font-size: 24px;
  margin-bottom: 8px;
}

.employee-drop-zone span {
  font-size: 14px;
  text-align: center;
}

.employee-drop-zone.active {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: var(--rewe-red);
  color: var(--rewe-red);
}

.standard-workweek-setting {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.standard-workweek-setting input {
  width: 60px;
  margin: 0 10px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.staffing-areas {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  margin-bottom: 30px;
  max-width: 100%;
  overflow-x: auto;
}

.staffing-area {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px;
  min-height: 300px;
  transition: all 0.3s ease;
}

.staffing-area.enlarged {
  grid-column: span 2;
  grid-row: span 2;
  transform: scale(1.05);
  z-index: 5;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.staffing-area-buttons {
  display: flex;
  gap: 5px;
}

.staffing-area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.staffing-area-title {
  margin: 0;
  color: var(--rewe-red);
  font-size: 1.17em;
  font-weight: bold;
}

.staffing-area-title-edit {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  font-size: 1.17em;
  font-weight: bold;
  color: var(--rewe-red);
  width: 100%;
}

.staffing-area-edit-button {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 5px;
  transition: transform 0.2s;
}

.staffing-area-edit-button:hover {
  transform: scale(1.2);
}

.employee-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.employee-list-filters {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.employee-list-search {
  position: relative;
}

.employee-list-search input {
  width: 100%;
  padding: 8px 10px;
  padding-left: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.employee-list-search i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--rewe-medium-gray);
}

.employee-list-status-filter select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  color: var(--rewe-dark-gray);
  cursor: pointer;
}

.employee-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  max-height: calc(100vh - 350px);
  scrollbar-width: thin;
  scrollbar-color: var(--rewe-medium-gray) #f0f0f0;
}

.employee-list::-webkit-scrollbar {
  width: 8px;
}

.employee-list::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.employee-list::-webkit-scrollbar-thumb {
  background-color: var(--rewe-medium-gray);
  border-radius: 4px;
  border: 2px solid #f0f0f0;
}

.employee-list-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: grab;
  background-color: #f8f9fa;
  margin-bottom: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
  position: relative;
}

.employee-list-item:hover {
  background-color: #e9ecef;
}

.employee-list-item:last-child {
  border-bottom: none;
}

.employee-list-item.dragging {
  opacity: 0.5;
  background-color: #e9ecef;
}

.employee-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.employee-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.employee-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85em;
}

.employee-market, .employee-hours, .employee-salary {
  color: #666;
}

.employee-market i, .employee-hours i, .employee-salary i {
  width: 14px;
  text-align: center;
  margin-right: 4px;
  color: var(--rewe-red);
}

.employee-detail-button {
  background-color: var(--rewe-red);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 0.85em;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-left: auto;
}

.employee-detail-button:hover {
  background-color: #c00;
}

.staffing-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
  table-layout: fixed;
  font-size: 0.85em;
}

.staffing-table th {
  background-color: #f5f5f5;
  padding: 5px 3px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #ddd;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.staffing-table td {
  padding: 5px 3px;
  border-bottom: 1px solid #eee;
}

/* Standardmäßig kein Umbruch für bessere Lesbarkeit */
.staffing-table td.no-wrap {
  white-space: nowrap;
  overflow: visible;
}

.staffing-table tr:hover {
  background-color: #f9f9f9;
}

.staffing-table tr.dragging {
  opacity: 0.5;
  background-color: #f0f0f0;
}

.staffing-table tr.starred-employee {
  background-color: #fff8e1;
}

.staffing-table tr.starred-employee:hover {
  background-color: #fff3cd;
}

.staffing-action-button.star-button {
  color: #ffc107;
}

.staffing-action-button.star-button.active {
  color: #ff9800;
}

/* Benutzerdefinierte Abteilung (Fremddienstleister) */
.custom-area-content {
  width: 100%;
}

.custom-table {
  width: 100%;
}

.custom-input {
  width: 100%;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  transition: border-color 0.2s;
}

.custom-input:focus {
  border-color: var(--rewe-red);
  outline: none;
}

.add-entry-button, .delete-entry-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
}

.add-entry-button {
  color: var(--rewe-success);
}

.add-entry-button:hover {
  background-color: rgba(40, 167, 69, 0.1);
}

.add-entry-button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.delete-entry-button {
  color: var(--rewe-danger);
}

.delete-entry-button:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.staffing-action-button.details-button {
  color: #4a90e2;
}

/* Kontextmenü */
.context-menu {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-width: 200px;
}

.context-menu-item {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
}

/* Mitarbeiter-Overlay */
.employee-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.employee-overlay-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.employee-overlay-header {
  background-color: var(--rewe-red);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.employee-overlay-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.employee-overlay-header .close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2em;
  cursor: pointer;
}

.employee-overlay-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 70vh;
}

.employee-profile {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.profile-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1em;
  color: var(--rewe-red);
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.profile-section h3 i {
  margin-right: 8px;
}

.profile-grid {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 10px;
}

.profile-label {
  font-weight: 600;
  color: var(--rewe-medium-gray);
}

.profile-value {
  color: var(--rewe-dark-gray);
}

.add-employee-button {
  width: 100%;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.add-employee-button:hover {
  background-color: #e9e9e9;
  border-color: #ccc;
}

.add-employee-button i {
  margin-right: 8px;
}

.staffing-summary {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.staffing-summary h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--rewe-red);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.summary-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.summary-item.full-width {
  grid-column: 1 / -1;
}

.summary-label {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--rewe-dark-gray);
}

.summary-value {
  font-size: 1.2em;
  color: var(--rewe-red);
}

.department-costs {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.department-cost {
  background-color: white;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.form-group input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-preview {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-top: 20px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.cancel-button, .save-button {
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
}

.save-button {
  background-color: var(--rewe-red);
  color: white;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-info {
  display: flex;
  justify-content: center;
  color: #999;
  cursor: help;
}

.action-button, .staffing-action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  transition: transform 0.2s;
}

.action-button:hover, .staffing-action-button:hover {
  transform: scale(1.2);
}

.action-button.edit-button, .staffing-action-button.edit-button {
  color: #007bff;
}

.action-button.delete-button, .staffing-action-button.delete-button {
  color: var(--rewe-red);
}

.employees-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-bar i {
  color: var(--rewe-medium-gray);
  margin-right: 8px;
}

.search-bar input {
  border: none;
  outline: none;
  width: 250px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  background-color: white;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-size: 0.8em;
  margin-bottom: 5px;
  color: var(--rewe-medium-gray);
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  min-width: 150px;
}

.filter-reset-button {
  margin-left: auto;
  align-self: flex-end;
  background-color: var(--rewe-light-gray);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
}

.employee-table-container {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.employee-table {
  width: 100%;
  border-collapse: collapse;
}

.employee-table th {
  background-color: var(--rewe-light-gray);
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.employee-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.employee-table tr:last-child td {
  border-bottom: none;
}

.employee-table tr:hover {
  background-color: var(--rewe-light-gray);
}

.employee-action-button {
  background-color: var(--rewe-red);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9em;
}

.employee-action-button i {
  margin-right: 5px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 600;
}

.status-badge.active {
  background-color: var(--rewe-success);
  color: white;
}

.status-badge.inactive {
  background-color: var(--rewe-danger);
  color: white;
}

.status-badge.exiting {
  background-color: var(--rewe-warning);
  color: var(--rewe-dark-gray);
}

.status-badge.unclear {
  background-color: #f39c12; /* Orange für "zu klären"-Status */
  color: white;
}

.status-container {
  display: flex;
  align-items: center;
}

.edit-status-button {
  margin-left: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--rewe-dark-gray);
  font-size: 0.9em;
}

.edit-status-button:hover {
  color: var(--rewe-orange);
}

.no-data {
  text-align: center;
  padding: 20px;
  color: var(--rewe-medium-gray);
}

/* Mitarbeiter-Details */
.employee-detail {
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.detail-header-title {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.document-buttons {
  display: flex;
  gap: 8px; /* Reduzierter Abstand zwischen den Schaltflächen */
  margin-top: 5px;
}

.document-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.document-button:hover {
  background-color: var(--rewe-light-gray);
}

.document-button i {
  font-size: 1.5em;
  margin-bottom: 5px;
  color: var(--rewe-red);
}

.button-label {
  font-size: 0.8em;
  color: var(--rewe-medium-gray);
  text-transform: capitalize; /* Erster Buchstabe groß */
  white-space: nowrap; /* Verhindert Zeilenumbrüche */
  text-align: center; /* Zentriert den Text */
}

/* Spezielle Formatierung für das Gesundheitszeugnis-Label */
.gesundheitszeugnis-label {
  white-space: normal; /* Erlaubt Zeilenumbrüche */
  line-height: 1.2; /* Reduzierter Zeilenabstand für kompaktere Darstellung */
}

.document-separator {
  width: 1px;
  height: 40px;
  background-color: #ddd;
  margin: 0 8px; /* Reduzierter Abstand für den Separator */
}

.document-status-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: 2px solid transparent;
  border-radius: 5px;
  cursor: pointer;
  padding: 8px 8px 4px 8px; /* Reduzierter Abstand unten */
  transition: all 0.2s;
  width: 100px; /* Etwas schmalere Schaltflächen */
}

.document-status-button:hover {
  background-color: var(--rewe-light-gray);
}

.document-status-button i {
  font-size: 1.5em;
  margin-bottom: 5px;
  color: var(--rewe-dark-gray);
}

.document-available {
  border-color: var(--rewe-success);
}

.document-available i {
  color: var(--rewe-success);
}

.document-missing {
  border-color: var(--rewe-danger);
}

.document-missing i {
  color: var(--rewe-danger);
}

.document-status-button {
  position: relative;
}

.missing-docs-indicator {
  display: flex;
  align-items: center;
  color: var(--rewe-danger);
  font-size: 0.9em;
  cursor: help;
}

.missing-docs-indicator i {
  margin-right: 5px;
}

/* Dokument-Upload-Dialog */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.document-upload-modal {
  width: 500px;
  max-width: 90%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--rewe-red);
  color: white;
}

.modal-title {
  margin: 0;
  font-size: 1.2em;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5em;
  cursor: pointer;
  line-height: 1;
}

.modal-body {
  padding: 20px;
}

.document-upload-container {
  margin-bottom: 20px;
}

.document-upload-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  text-align: center;
}

.upload-icon-container {
  width: 80px;
  height: 80px;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  border: 1px solid #eee;
}

.upload-icon-container i {
  font-size: 2.5em;
  color: var(--rewe-red);
}

.upload-title {
  font-size: 1.2em;
  color: var(--rewe-dark-gray);
  margin: 0 0 15px 0;
}

.upload-instruction {
  color: var(--rewe-medium-gray);
  margin: 10px 0;
  font-size: 0.95em;
}

.upload-formats {
  color: var(--rewe-medium-gray);
  margin: 5px 0 20px;
  font-size: 0.85em;
}

.file-select-button {
  display: inline-block;
  background-color: var(--rewe-red);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.file-select-button:hover {
  background-color: #a80619;
}

.file-select-button input {
  display: none;
}

.document-preview-container {
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.document-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin-bottom: 10px;
}

.document-count {
  font-size: 0.9em;
  color: var(--rewe-medium-gray);
}

.document-nav-buttons {
  display: flex;
  gap: 10px;
}

.nav-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background-color: white;
  color: var(--rewe-medium-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.nav-button:hover:not(:disabled) {
  background-color: var(--rewe-red);
  color: white;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.document-preview-content {
  margin-bottom: 15px;
}

.document-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.document-preview-header h4 {
  margin: 0;
  color: var(--rewe-dark-gray);
}

.remove-file-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 1.2em;
  transition: color 0.2s;
}

.remove-file-button:hover {
  color: var(--rewe-red);
}

.document-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  max-width: 400px;
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  position: relative;
}

.document-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(204, 7, 30, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.document-icon i {
  font-size: 1.8em;
  color: var(--rewe-red);
}

.document-details {
  flex: 1;
}

.document-name {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: var(--rewe-dark-gray);
}

.document-size {
  margin: 0;
  font-size: 0.85em;
  color: var(--rewe-medium-gray);
}

.image-preview-container {
  background-color: white;
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.document-image-preview {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.document-actions-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
  width: 100%;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.document-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  width: 100%;
}

.document-action-btn i {
  margin-right: 8px;
}

.view-btn {
  background-color: #4a90e2;
  color: white;
}

.view-btn:hover {
  background-color: #3a7bc8;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.upload-more-btn {
  background-color: #4CAF50;
  color: white;
}

.upload-more-btn:hover {
  background-color: #45a049;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.cancel-button {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.upload-button {
  background-color: var(--rewe-red);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.upload-button:hover {
  background-color: #a80619;
}

.upload-button:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
}

.upload-button:disabled:hover {
  background-color: #ccc;
}

/* Dashboard Styles */
.warning-card {
  background-color: #fff8e1;
  border-left: 4px solid var(--rewe-danger);
}

.warning-card h3 {
  color: var(--rewe-danger);
}

.health-certificate-card {
  background-color: #ffebee;
  border-left: 4px solid #e74c3c;
}

.health-certificate-card h3 {
  color: #e74c3c;
  display: flex;
  align-items: center;
}

.health-certificate-card h3 i {
  margin-right: 8px;
}

.back-button, .edit-button {
  display: flex;
  align-items: center;
  background-color: var(--rewe-light-gray);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  margin-left: 10px;
}

.back-button i, .edit-button i {
  margin-right: 5px;
}

.edit-button {
  background-color: var(--rewe-orange);
  color: var(--rewe-dark-gray);
}

.edit-button:hover {
  background-color: #e6b800;
}

.edit-button:disabled {
  background-color: #f5f5f5;
  color: #aaa;
  cursor: not-allowed;
}

.employee-profile {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.profile-section {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.profile-section-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.profile-section-half {
  flex: 1;
  margin-bottom: 0;
}

.profile-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--rewe-red);
  display: flex;
  align-items: center;
}

.profile-section h3 i {
  margin-right: 8px;
}

.profile-grid {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 10px;
}

.profile-grid-full {
  grid-column: span 2;
  margin-top: 15px;
  text-align: center;
}

.profile-label {
  font-weight: 600;
  color: var(--rewe-medium-gray);
}

.profile-value {
  color: var(--rewe-dark-gray);
}

.department-with-icon {
  display: flex;
  align-items: center;
}

.department-with-icon i {
  margin-left: 8px;
  color: #e74c3c; /* Rot für das Gesundheitszeugnis-Icon */
}

.health-certificate-text {
  margin-left: 5px;
  font-size: 0.9em;
  color: #e74c3c;
}

.editable-field {
  margin-right: 10px;
}

.fas.fa-history {
  color: #888;
  cursor: pointer;
  font-size: 0.9em;
}

.fas.fa-history:hover {
  color: #555;
}

.edit-field-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.edit-field {
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
}

.edit-buttons {
  display: flex;
  gap: 8px;
}

.save-button, .cancel-button {
  padding: 4px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.save-button {
  background-color: #cc071e;
  color: white;
}

.save-button:hover {
  background-color: #a80619;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.ml-2 {
  margin-left: 8px;
}

.history-title {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 1em;
}

.conversations-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 10px 0;
}

.add-conversation-button {
  align-self: flex-start;
  margin-top: 10px;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th {
  background-color: var(--rewe-light-gray);
  padding: 8px 10px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.history-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
}

/* Import-Bereich */
.import-container {
  padding: 20px;
}

.import-controls {
  display: flex;
  align-items: flex-end;
  gap: 15px;
  margin-bottom: 20px;
}

.import-table-container {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.import-table {
  width: 100%;
  border-collapse: collapse;
}

.import-table th {
  background-color: var(--rewe-light-gray);
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.import-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.import-button {
  background-color: var(--rewe-orange);
  color: var(--rewe-dark-gray);
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9em;
}

.import-instructions {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.import-instructions h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--rewe-red);
}

.import-instructions ul {
  margin-left: 20px;
}

.import-instructions li {
  margin-bottom: 5px;
}

.error {
  background-color: var(--rewe-danger);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success {
  background-color: var(--rewe-success);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px;
  font-size: 1.2em;
  color: var(--rewe-medium-gray);
}

.loading i {
  margin-right: 10px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Debug-Buttons */
.debug-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.debug-button {
  background-color: #6f42c1;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.debug-button:hover {
  background-color: #5a32a3;
}

.debug-button:disabled {
  background-color: #b8a2e3;
  cursor: not-allowed;
}

/* Datenbank-Info-Anzeige */
.database-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.database-section {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.database-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.database-stats,
.database-file-info,
.database-upload-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.database-stat,
.database-file-stat,
.database-upload-stat {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.database-stat-label,
.database-file-label,
.database-upload-label {
  font-weight: bold;
}

.database-upload-files {
  margin-top: 15px;
}

.database-upload-files h5 {
  margin-top: 0;
  margin-bottom: 10px;
}

.database-upload-files ul {
  list-style-type: disc;
  padding-left: 20px;
}

.database-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.database-table th,
.database-table td {
  padding: 8px;
  text-align: left;
  border: 1px solid #ddd;
}

.database-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.database-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.database-import-status {
  background-color: #282c34;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  white-space: pre-wrap;
}

/* Modal für Gehaltsdaten */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 4px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.fullscreen {
  width: 100%;
  max-width: 100%;
  height: 100vh;
  max-height: 100vh;
  border-radius: 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--rewe-red);
  color: white;
}

.modal-header h3 {
  margin: 0;
}

.modal-title {
  color: white !important;
}

.modal-actions {
  display: flex;
  align-items: center;
}

.modal-header .fullscreen-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  margin-right: 15px;
}

.modal-header .close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

.modal-content.fullscreen .modal-body {
  max-height: calc(100vh - 60px);
}

.salary-table-container {
  width: 100%;
  overflow-x: auto;
}

.salary-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: auto;
}

.salary-table thead.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

.salary-table th {
  background-color: var(--rewe-light-gray);
  padding: 10px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

.salary-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
}

.salary-table tr:hover {
  background-color: var(--rewe-light-gray);
}

/* Kündigungsgenerator */
.termination-generator {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.termination-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.termination-icon {
  background-color: var(--rewe-red);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
}

.termination-header h2 {
  margin: 0 0 5px 0;
  color: var(--rewe-dark-gray);
  font-size: 24px;
}

.termination-header p {
  margin: 0;
  color: var(--rewe-medium-gray);
}

.termination-form {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--rewe-dark-gray);
}

.employee-search {
  position: relative;
}

.search-input, .text-input, .date-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.search-input:focus, .text-input:focus, .date-input:focus {
  outline: none;
  border-color: var(--rewe-red);
  box-shadow: 0 0 0 2px rgba(227, 6, 19, 0.1);
}

.employee-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.employee-option {
  width: 100%;
  padding: 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.employee-option:hover {
  background-color: #f8f9fa;
}

.employee-option:last-child {
  border-bottom: none;
}

.employee-name {
  font-weight: 600;
  color: var(--rewe-dark-gray);
}

.employee-details {
  font-size: 14px;
  color: var(--rewe-medium-gray);
  margin-top: 2px;
}

.selected-employee {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 25px;
  border-left: 4px solid var(--rewe-red);
}

.selected-employee h4 {
  margin: 0 0 15px 0;
  color: var(--rewe-dark-gray);
}

.employee-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.info-row {
  font-size: 14px;
}

.info-row span {
  font-weight: 600;
  color: var(--rewe-medium-gray);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s;
}

.radio-label:hover {
  background-color: #f8f9fa;
}

.radio-label input[type="radio"] {
  margin-right: 12px;
  accent-color: var(--rewe-red);
}

.radio-label input[type="radio"]:checked + span {
  color: var(--rewe-red);
  font-weight: 600;
}

.termination-preview {
  background-color: #e3f2fd;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 25px;
  border-left: 4px solid #2196f3;
}

.termination-preview h4 {
  margin: 0 0 15px 0;
  color: #1976d2;
}

.preview-content p {
  margin: 8px 0;
  font-size: 14px;
}

.preview-content strong {
  color: var(--rewe-dark-gray);
}

.probation-notice {
  background-color: #fff3cd;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #ffc107;
  margin-top: 10px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

.submit-button {
  background-color: var(--rewe-red);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: #c00;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #dc3545;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #28a745;
}

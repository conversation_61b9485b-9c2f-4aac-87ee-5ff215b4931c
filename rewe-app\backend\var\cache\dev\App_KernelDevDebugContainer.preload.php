<?php

// This file has been auto-generated by the Symfony Dependency Injection Component
// You can reference it in the "opcache.preload" php.ini setting on PHP >= 7.4 when preloading is desired

use Symfony\Component\DependencyInjection\Dumper\Preloader;

if (in_array(PHP_SAPI, ['cli', 'phpdbg', 'embed'], true)) {
    return;
}

require dirname(__DIR__, 3).''.\DIRECTORY_SEPARATOR.'vendor/autoload.php';
(require __DIR__.'/App_KernelDevDebugContainer.php')->set(\ContainerOgi04LG\App_KernelDevDebugContainer::class, null);
require __DIR__.'/ContainerOgi04LG/UriSignerGhostB68a0a1.php';
require __DIR__.'/ContainerOgi04LG/EntityManagerGhost614a58f.php';
require __DIR__.'/ContainerOgi04LG/RequestPayloadValueResolverGhost01ca9cc.php';
require __DIR__.'/ContainerOgi04LG/ProfilerProxy8977808.php';
require __DIR__.'/ContainerOgi04LG/getWebProfiler_Controller_RouterService.php';
require __DIR__.'/ContainerOgi04LG/getWebProfiler_Controller_ProfilerService.php';
require __DIR__.'/ContainerOgi04LG/getWebProfiler_Controller_ExceptionPanelService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_WhenService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_NotCompromisedPasswordService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_NoSuspiciousCharactersService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_ExpressionLanguageService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_ExpressionService.php';
require __DIR__.'/ContainerOgi04LG/getValidator_EmailService.php';
require __DIR__.'/ContainerOgi04LG/getUriSignerService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Runtime_SerializerService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Runtime_SecurityCsrfService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Runtime_ImportmapService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Runtime_HttpkernelService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Mailer_MessageListenerService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Form_RendererService.php';
require __DIR__.'/ContainerOgi04LG/getTwig_Form_EngineService.php';
require __DIR__.'/ContainerOgi04LG/getTurbo_Twig_RuntimeService.php';
require __DIR__.'/ContainerOgi04LG/getTurbo_Doctrine_EventListenerService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_YmlService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_XliffService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_ResService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_QtService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_PoService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_PhpService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_MoService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_JsonService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_IniService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_DatService.php';
require __DIR__.'/ContainerOgi04LG/getTranslation_Loader_CsvService.php';
require __DIR__.'/ContainerOgi04LG/getTexter_TransportsService.php';
require __DIR__.'/ContainerOgi04LG/getStimulus_UxControllersTwigRuntimeService.php';
require __DIR__.'/ContainerOgi04LG/getStimulus_AssetMapper_LoaderJavascriptCompilerService.php';
require __DIR__.'/ContainerOgi04LG/getStimulus_AssetMapper_ControllersMapGeneratorService.php';
require __DIR__.'/ContainerOgi04LG/getSession_Handler_NativeService.php';
require __DIR__.'/ContainerOgi04LG/getSession_FactoryService.php';
require __DIR__.'/ContainerOgi04LG/getServicesResetterService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Validator_UserPasswordService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_RouteLoader_LogoutService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_PasswordHasherFactoryService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Logout_Listener_CsrfTokenClearingService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_UserProviderService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_UserChecker_MainService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_Session_MainService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_PasswordMigratingService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_Main_UserProviderService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_CsrfProtectionService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Listener_CheckAuthenticatorCredentialsService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Firewall_Map_Context_MainService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_Firewall_Map_Context_DevService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_ChannelListenerService.php';
require __DIR__.'/ContainerOgi04LG/getSecurity_AccessListenerService.php';
require __DIR__.'/ContainerOgi04LG/getSecrets_VaultService.php';
require __DIR__.'/ContainerOgi04LG/getSecrets_EnvVarLoaderService.php';
require __DIR__.'/ContainerOgi04LG/getRouting_LoaderService.php';
require __DIR__.'/ContainerOgi04LG/getPropertyInfo_SerializerExtractorService.php';
require __DIR__.'/ContainerOgi04LG/getNotifier_TransportFactory_NullService.php';
require __DIR__.'/ContainerOgi04LG/getMonolog_Logger_MessengerService.php';
require __DIR__.'/ContainerOgi04LG/getMonolog_Logger_MailerService.php';
require __DIR__.'/ContainerOgi04LG/getMonolog_Logger_DeprecationService.php';
require __DIR__.'/ContainerOgi04LG/getMonolog_Logger_AssetMapperService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_TransportFactoryService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Transport_Sync_FactoryService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Transport_InMemory_FactoryService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Transport_FailedService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Transport_Doctrine_FactoryService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Transport_AsyncService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_RoutableMessageBusService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Retry_SendFailedMessageForRetryListenerService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Retry_MultiplierRetryStrategy_FailedService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Retry_MultiplierRetryStrategy_AsyncService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Listener_StopWorkerOnRestartSignalListenerService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Failure_SendFailedMessageToFailureTransportListenerService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Bus_Default_Middleware_TraceableService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Bus_Default_Middleware_SendMessageService.php';
require __DIR__.'/ContainerOgi04LG/getMessenger_Bus_Default_Middleware_HandleMessageService.php';
require __DIR__.'/ContainerOgi04LG/getMailer_TransportsService.php';
require __DIR__.'/ContainerOgi04LG/getMailer_TransportFactory_SmtpService.php';
require __DIR__.'/ContainerOgi04LG/getMailer_TransportFactory_SendmailService.php';
require __DIR__.'/ContainerOgi04LG/getMailer_TransportFactory_NullService.php';
require __DIR__.'/ContainerOgi04LG/getMailer_TransportFactory_NativeService.php';
require __DIR__.'/ContainerOgi04LG/getFragment_Renderer_InlineService.php';
require __DIR__.'/ContainerOgi04LG/getErrorHandler_ErrorRenderer_HtmlService.php';
require __DIR__.'/ContainerOgi04LG/getErrorControllerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_UuidGeneratorService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_UlidGeneratorService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Validator_UniqueService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Messenger_EventSubscriber_DoctrineClearEntityManagerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Messenger_DoctrineSchemaListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Listeners_PdoSessionHandlerSchemaListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Listeners_LockStoreSchemaListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Listeners_DoctrineTokenProviderSchemaListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_Listeners_DoctrineDbalCacheAdapterSchemaListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_DefaultListeners_AttachEntityListenersService.php';
require __DIR__.'/ContainerOgi04LG/getDoctrine_Orm_DefaultEntityManager_PropertyInfoExtractorService.php';
require __DIR__.'/ContainerOgi04LG/getDebug_Security_Voter_VoteListenerService.php';
require __DIR__.'/ContainerOgi04LG/getDebug_Security_Firewall_Authenticator_MainService.php';
require __DIR__.'/ContainerOgi04LG/getDebug_FileLinkFormatter_UrlFormatService.php';
require __DIR__.'/ContainerOgi04LG/getDebug_ErrorHandlerConfiguratorService.php';
require __DIR__.'/ContainerOgi04LG/getDataCollector_Request_SessionCollectorService.php';
require __DIR__.'/ContainerOgi04LG/getController_TemplateAttributeListenerService.php';
require __DIR__.'/ContainerOgi04LG/getContainer_GetenvService.php';
require __DIR__.'/ContainerOgi04LG/getContainer_GetRoutingConditionServiceService.php';
require __DIR__.'/ContainerOgi04LG/getContainer_EnvVarProcessorsLocatorService.php';
require __DIR__.'/ContainerOgi04LG/getContainer_EnvVarProcessorService.php';
require __DIR__.'/ContainerOgi04LG/getCache_SystemClearerService.php';
require __DIR__.'/ContainerOgi04LG/getCache_GlobalClearerService.php';
require __DIR__.'/ContainerOgi04LG/getCache_AppClearerService.php';
require __DIR__.'/ContainerOgi04LG/getAssetMapper_Importmap_GeneratorService.php';
require __DIR__.'/ContainerOgi04LG/getAssetMapper_Importmap_ConfigReaderService.php';
require __DIR__.'/ContainerOgi04LG/getAssetMapper_Compiler_JavascriptImportPathCompilerService.php';
require __DIR__.'/ContainerOgi04LG/getAssetMapper_Compiler_CssAssetUrlCompilerService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Validator_State_ErrorProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Symfony_MainControllerService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_SwaggerUi_ProcessorService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_SwaggerUi_ContextService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_SwaggerUi_ActionService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProvider_ParameterValidatorService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProvider_ObjectService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProvider_CreateService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProvider_ContentNegotiationService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProvider_BackedEnumService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_StateProcessor_WriteService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_State_ErrorProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Serializer_FilterParameterProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Openapi_SerializerContextBuilderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Openapi_ProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Openapi_OptionsService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Openapi_FactoryService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Metadata_PropertySchema_OneOfRestrictionService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Metadata_PropertySchema_CollectionRestrictionService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Listener_ExceptionService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Jsonld_Action_ContextService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_JsonSchema_BackwardCompatibleSchemaFactoryService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_State_RemoveProcessorService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_State_PersistProcessorService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_State_ItemProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_State_CollectionProviderService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_QueryExtension_PaginationService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_QueryExtension_FilterEagerLoadingService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_QueryExtension_FilterService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_QueryExtension_EagerLoadingService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_LinksHandlerService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Doctrine_Orm_Extension_ParameterExtensionService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Action_NotFoundService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Action_NotExposedService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Action_EntrypointService.php';
require __DIR__.'/ContainerOgi04LG/getApiPlatform_Action_DocumentationService.php';
require __DIR__.'/ContainerOgi04LG/getTemplateControllerService.php';
require __DIR__.'/ContainerOgi04LG/getRedirectControllerService.php';
require __DIR__.'/ContainerOgi04LG/getProfilerControllerService.php';
require __DIR__.'/ContainerOgi04LG/getProcessorService.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_ZHyJIs5_KernelregisterContainerConfigurationService.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_ZHyJIs5_KernelloadRoutesService.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_ZHyJIs5Service.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_JsQaLu1Service.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_AVKDDNUService.php';
require __DIR__.'/ContainerOgi04LG/get_ServiceLocator_F6vdjrPService.php';
require __DIR__.'/ContainerOgi04LG/get_Security_RequestMatcher_GOpgIHxService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_JyyWvHwService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_GEx8y9aService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_WG_ORBvService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_UKIJHilService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_Qv3faSNService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_NumTeF8Service.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_EXsqOW5Service.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_Dkv9sRYService.php';
require __DIR__.'/ContainerOgi04LG/get_Messenger_HandlerDescriptor_Die6BxeService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_Security_UserValueResolverService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_Security_SecurityTokenValueResolverService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_Doctrine_Orm_EntityValueResolverService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_VariadicService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_UidService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_SessionService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_ServiceService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_RequestPayloadService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_RequestAttributeService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_RequestService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_QueryParameterValueResolverService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_NotTaggedControllerService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_DefaultService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_DatetimeService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ArgumentResolver_BackedEnumResolverService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_ValueResolver_ApiPlatform_ArgumentResolver_PayloadService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_Security_Voter_Security_Access_SimpleRoleVoterService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_Security_Voter_Security_Access_ExpressionVoterService.php';
require __DIR__.'/ContainerOgi04LG/get_Debug_Security_Voter_Security_Access_AuthenticatedVoterService.php';

$classes = [];
$classes[] = 'Symfony\Bundle\FrameworkBundle\FrameworkBundle';
$classes[] = 'Symfony\Bundle\TwigBundle\TwigBundle';
$classes[] = 'Symfony\Bundle\SecurityBundle\SecurityBundle';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DoctrineBundle';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle';
$classes[] = 'Nelmio\CorsBundle\NelmioCorsBundle';
$classes[] = 'ApiPlatform\Symfony\Bundle\ApiPlatformBundle';
$classes[] = 'Symfony\Bundle\DebugBundle\DebugBundle';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\WebProfilerBundle';
$classes[] = 'Symfony\UX\StimulusBundle\StimulusBundle';
$classes[] = 'Symfony\UX\Turbo\TurboBundle';
$classes[] = 'Twig\Extra\TwigExtraBundle\TwigExtraBundle';
$classes[] = 'Symfony\Bundle\MonologBundle\MonologBundle';
$classes[] = 'Symfony\Bundle\MakerBundle\MakerBundle';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\Profiler';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\FileProfilerStorage';
$classes[] = 'Monolog\Logger';
$classes[] = 'Symfony\Component\Console\DataCollector\CommandDataCollector';
$classes[] = 'ApiPlatform\Symfony\Bundle\DataCollector\RequestDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\TimeDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\MemoryDataCollector';
$classes[] = 'Symfony\Component\Validator\DataCollector\ValidatorDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\AjaxDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ExceptionDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\LoggerDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\EventDataCollector';
$classes[] = 'Symfony\Component\Translation\DataCollector\TranslationDataCollector';
$classes[] = 'Symfony\Bundle\SecurityBundle\DataCollector\SecurityDataCollector';
$classes[] = 'Symfony\Bridge\Twig\DataCollector\TwigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\DataCollector\HttpClientDataCollector';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DataCollector\DoctrineDataCollector';
$classes[] = 'Symfony\Component\Messenger\DataCollector\MessengerDataCollector';
$classes[] = 'Symfony\Component\Mailer\DataCollector\MessageDataCollector';
$classes[] = 'Symfony\Component\Notifier\DataCollector\NotificationDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\TraceableHttpClient';
$classes[] = 'Symfony\Component\HttpClient\UriTemplateHttpClient';
$classes[] = 'Symfony\Contracts\HttpClient\HttpClientInterface';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\TraceableVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\ExpressionVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\RoleVoter';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver';
$classes[] = 'ApiPlatform\Symfony\Bundle\ArgumentResolver\PayloadArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\NotTaggedControllerValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\UidValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver';
$classes[] = 'Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver';
$classes[] = 'Symfony\Component\ExpressionLanguage\ExpressionLanguage';
$classes[] = 'Symfony\Bridge\Doctrine\Attribute\MapEntity';
$classes[] = 'Symfony\Component\Security\Http\Controller\SecurityTokenValueResolver';
$classes[] = 'Symfony\Component\Security\Http\Controller\UserValueResolver';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlerDescriptor';
$classes[] = 'Symfony\Component\Process\Messenger\RunProcessMessageHandler';
$classes[] = 'Symfony\Component\Notifier\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Console\Messenger\RunCommandMessageHandler';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Console\Application';
$classes[] = 'Symfony\Component\HttpClient\Messenger\PingWebhookMessageHandler';
$classes[] = 'Symfony\Component\Mailer\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Notifier\Transport\Transports';
$classes[] = 'Symfony\Component\Notifier\Transport';
$classes[] = 'Symfony\Component\Messenger\Handler\RedispatchMessageHandler';
$classes[] = 'Symfony\Component\HttpFoundation\ChainRequestMatcher';
$classes[] = 'Symfony\Component\HttpFoundation\RequestMatcher\PathRequestMatcher';
$classes[] = 'Symfony\Component\DependencyInjection\ServiceLocator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\VirtualRequestStack';
$classes[] = 'ApiPlatform\Symfony\Messenger\Processor';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\RedirectController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\TemplateController';
$classes[] = 'ApiPlatform\Symfony\Action\DocumentationAction';
$classes[] = 'ApiPlatform\Symfony\Action\EntrypointAction';
$classes[] = 'ApiPlatform\Symfony\Action\NotExposedAction';
$classes[] = 'ApiPlatform\Symfony\Action\NotFoundAction';
$classes[] = 'Symfony\Component\Cache\Adapter\TraceableAdapter';
$classes[] = 'Symfony\Component\Cache\Adapter\AdapterInterface';
$classes[] = 'Symfony\Component\Cache\Adapter\AbstractAdapter';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\ParameterExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\LinksHandler';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\EagerLoadingExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\FilterExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\FilterEagerLoadingExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\OrderExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\PaginationExtension';
$classes[] = 'ApiPlatform\State\Pagination\Pagination';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\CollectionProvider';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\ItemProvider';
$classes[] = 'ApiPlatform\Doctrine\Common\State\PersistProcessor';
$classes[] = 'ApiPlatform\Doctrine\Common\State\RemoveProcessor';
$classes[] = 'ApiPlatform\Hydra\Serializer\HydraPrefixNameConverter';
$classes[] = 'ApiPlatform\JsonSchema\BackwardCompatibleSchemaFactory';
$classes[] = 'ApiPlatform\Hydra\JsonSchema\SchemaFactory';
$classes[] = 'ApiPlatform\JsonSchema\SchemaFactory';
$classes[] = 'ApiPlatform\JsonSchema\DefinitionNameFactory';
$classes[] = 'ApiPlatform\JsonLd\Action\ContextAction';
$classes[] = 'ApiPlatform\JsonLd\ContextBuilder';
$classes[] = 'ApiPlatform\Symfony\EventListener\ExceptionListener';
$classes[] = 'ApiPlatform\Symfony\EventListener\ErrorListener';
$classes[] = 'ApiPlatform\Metadata\Operation\Factory\OperationMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\CachedPropertyMetadataFactory';
$classes[] = 'ApiPlatform\JsonSchema\Metadata\Property\Factory\SchemaPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\ValidatorPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ExtractorPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\AttributePropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\IdentifierPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\DefaultPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\SerializerPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Property\DoctrineOrmPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\PropertyInfoPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\CachedPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ExtractorPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ConcernsPropertyNameCollectionMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\PropertyInfoPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Extractor\XmlPropertyExtractor';
$classes[] = 'ApiPlatform\Metadata\Extractor\YamlPropertyExtractor';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaChoiceRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaCollectionRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaCountRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaFormat';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaGreaterThanOrEqualRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaGreaterThanRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLengthRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLessThanOrEqualRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLessThanRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaOneOfRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaRangeRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaRegexRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaUniqueRestriction';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\CachedResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Resource\DoctrineOrmResourceCollectionMetadataFactory';
$classes[] = 'ApiPlatform\Symfony\Messenger\Metadata\MessengerResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\PhpDocResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AlternateUriResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\FiltersResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\FormatsResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\InputOutputResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\OperationNameResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\LinkResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Resource\DoctrineOrmLinkFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\LinkFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\MainControllerResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\UriTemplateResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Operation\UnderscorePathSegmentNameGenerator';
$classes[] = 'ApiPlatform\Metadata\Util\Inflector';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\BackedEnumResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\NotExposedOperationResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ExtractorResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ConcernsResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Validator\Metadata\Resource\Factory\ParameterValidationResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ParameterResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AttributesResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\CachedResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ExtractorResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ClassNameResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AttributesResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ConcernsResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Extractor\XmlResourceExtractor';
$classes[] = 'ApiPlatform\Metadata\Extractor\YamlResourceExtractor';
$classes[] = 'Negotiation\Negotiator';
$classes[] = 'ApiPlatform\OpenApi\Factory\OpenApiFactory';
$classes[] = 'ApiPlatform\State\Pagination\PaginationOptions';
$classes[] = 'ApiPlatform\OpenApi\Options';
$classes[] = 'ApiPlatform\OpenApi\State\OpenApiProvider';
$classes[] = 'ApiPlatform\OpenApi\Serializer\SerializerContextBuilder';
$classes[] = 'ApiPlatform\Serializer\SerializerFilterContextBuilder';
$classes[] = 'ApiPlatform\Serializer\SerializerContextBuilder';
$classes[] = 'ApiPlatform\Metadata\ResourceClassResolver';
$classes[] = 'ApiPlatform\Symfony\Routing\Router';
$classes[] = 'ApiPlatform\Symfony\Security\ResourceAccessChecker';
$classes[] = 'ApiPlatform\Serializer\Parameter\SerializerFilterParameterProvider';
$classes[] = 'ApiPlatform\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\LoaderChain';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\AttributeLoader';
$classes[] = 'ApiPlatform\State\ErrorProvider';
$classes[] = 'ApiPlatform\State\Processor\WriteProcessor';
$classes[] = 'ApiPlatform\State\Processor\SerializeProcessor';
$classes[] = 'ApiPlatform\HttpCache\State\AddHeadersProcessor';
$classes[] = 'ApiPlatform\State\Processor\AddLinkHeaderProcessor';
$classes[] = 'ApiPlatform\Hydra\State\HydraLinkProcessor';
$classes[] = 'ApiPlatform\State\Processor\RespondProcessor';
$classes[] = 'ApiPlatform\State\CallableProcessor';
$classes[] = 'ApiPlatform\State\Provider\BackedEnumProvider';
$classes[] = 'ApiPlatform\State\Provider\ContentNegotiationProvider';
$classes[] = 'ApiPlatform\State\Provider\ParameterProvider';
$classes[] = 'ApiPlatform\State\CreateProvider';
$classes[] = 'ApiPlatform\State\CallableProvider';
$classes[] = 'ApiPlatform\State\ObjectProvider';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ParameterValidatorProvider';
$classes[] = 'ApiPlatform\Symfony\Security\State\AccessCheckerProvider';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ValidateProvider';
$classes[] = 'ApiPlatform\State\Provider\DeserializeProvider';
$classes[] = 'ApiPlatform\State\Provider\SecurityParameterProvider';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiProvider';
$classes[] = 'ApiPlatform\State\Provider\ReadProvider';
$classes[] = 'ApiPlatform\Symfony\Validator\Validator';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiAction';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiContext';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiProcessor';
$classes[] = 'ApiPlatform\Symfony\Routing\IriConverter';
$classes[] = 'ApiPlatform\Metadata\IdentifiersExtractor';
$classes[] = 'ApiPlatform\Symfony\Routing\SkolemIriConverter';
$classes[] = 'ApiPlatform\Symfony\Controller\MainController';
$classes[] = 'ApiPlatform\Symfony\UriVariableTransformer\UlidUriVariableTransformer';
$classes[] = 'ApiPlatform\Symfony\UriVariableTransformer\UuidUriVariableTransformer';
$classes[] = 'ApiPlatform\Metadata\UriVariablesConverter';
$classes[] = 'ApiPlatform\Metadata\UriVariableTransformer\DateTimeUriVariableTransformer';
$classes[] = 'ApiPlatform\Metadata\UriVariableTransformer\IntegerUriVariableTransformer';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ErrorProvider';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapper';
$classes[] = 'Symfony\Component\AssetMapper\Factory\CachedMappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Factory\MappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Path\PublicAssetsPathResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperCompiler';
$classes[] = 'Symfony\Component\AssetMapper\CompiledAssetMapperConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\CssAssetUrlCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\JavaScriptImportPathCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\SourceMappingUrlsCompiler';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperDevServerSubscriber';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapGenerator';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\RemotePackageStorage';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperRepository';
$classes[] = 'Symfony\Component\Asset\Packages';
$classes[] = 'Symfony\Component\AssetMapper\MapperAwareAssetPackage';
$classes[] = 'Symfony\Component\Asset\PathPackage';
$classes[] = 'Symfony\Component\Asset\VersionStrategy\EmptyVersionStrategy';
$classes[] = 'Symfony\Component\Asset\Context\RequestStackContext';
$classes[] = 'Symfony\Component\Cache\Adapter\FilesystemAdapter';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer';
$classes[] = 'Symfony\Component\Cache\Marshaller\DefaultMarshaller';
$classes[] = 'Symfony\Component\Cache\Adapter\ArrayAdapter';
$classes[] = 'Symfony\Component\Clock\Clock';
$classes[] = 'Symfony\Component\Config\Resource\SelfCheckingResourceChecker';
$classes[] = 'Symfony\Component\Config\ResourceCheckerConfigCacheFactory';
$classes[] = 'Symfony\Component\DependencyInjection\EnvVarProcessor';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\CacheAttributeListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\IsCsrfTokenValidAttributeListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\IsGrantedAttributeListener';
$classes[] = 'Symfony\Bridge\Twig\EventListener\TemplateAttributeListener';
$classes[] = 'Symfony\Component\Cache\DataCollector\CacheDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\DumpDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataExtractor';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\RequestDataCollector';
$classes[] = 'Symfony\Bundle\FrameworkBundle\DataCollector\RouterDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DebugHandlersListener';
$classes[] = 'Symfony\Component\HttpKernel\Log\DebugLoggerConfigurator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\ErrorHandlerConfigurator';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter';
$classes[] = 'Symfony\Bridge\Monolog\Processor\DebugProcessor';
$classes[] = 'Symfony\Component\Security\Core\Authorization\TraceableAccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Strategy\AffirmativeStrategy';
$classes[] = 'Symfony\Component\EventDispatcher\Debug\TraceableEventDispatcher';
$classes[] = 'Symfony\Component\EventDispatcher\EventDispatcher';
$classes[] = 'Symfony\Bundle\SecurityBundle\Debug\TraceableFirewallListener';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\Debug\TraceableAuthenticatorManagerListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AuthenticatorManagerListener';
$classes[] = 'Symfony\Component\Security\Http\Authentication\AuthenticatorManager';
$classes[] = 'Symfony\Bundle\SecurityBundle\EventListener\VoteListener';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableSerializer';
$classes[] = 'Symfony\Component\Serializer\Serializer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UnwrappingDenormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\LegacyOpenApiNormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\ApiGatewayNormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\OpenApiNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ObjectNormalizer';
$classes[] = 'Symfony\Component\Serializer\Encoder\JsonEncoder';
$classes[] = 'ApiPlatform\Serializer\ConstraintViolationListNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\ConstraintViolationListNormalizer';
$classes[] = 'ApiPlatform\Symfony\Validator\Serializer\ValidationExceptionNormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ErrorNormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ItemNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\DocumentationNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\EntrypointNormalizer';
$classes[] = 'ApiPlatform\Serializer\ItemNormalizer';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\Normalizer\FlattenExceptionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ProblemNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UidNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ConstraintViolationListNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\MimeMessageNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\PropertyNormalizer';
$classes[] = 'Symfony\Component\Serializer\Mapping\ClassDiscriminatorFromClassMetadata';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeZoneNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateIntervalNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\FormErrorNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DataUriNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\TranslatableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\CollectionFiltersNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\PartialCollectionViewNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\CollectionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ArrayDenormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ObjectNormalizer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\XmlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\YamlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\CsvEncoder';
$classes[] = 'ApiPlatform\Serializer\JsonEncoder';
$classes[] = 'ApiPlatform\Serializer\YamlEncoder';
$classes[] = 'Symfony\Component\Stopwatch\Stopwatch';
$classes[] = 'Symfony\Component\Validator\Validator\TraceableValidator';
$classes[] = 'Symfony\Component\Validator\Validator\ValidatorInterface';
$classes[] = 'Symfony\Component\DependencyInjection\Config\ContainerParametersResourceChecker';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DisallowRobotsIndexingListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Registry';
$classes[] = 'Doctrine\DBAL\Connection';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ConnectionFactory';
$classes[] = 'Doctrine\DBAL\Configuration';
$classes[] = 'Doctrine\DBAL\Schema\LegacySchemaManagerFactory';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager';
$classes[] = 'Doctrine\DBAL\Logging\Middleware';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\DebugMiddleware';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\IdleConnectionMiddleware';
$classes[] = 'Doctrine\DBAL\Tools\DsnParser';
$classes[] = 'Symfony\Bridge\Doctrine\ContainerAwareEventManager';
$classes[] = 'Symfony\Bridge\Doctrine\Middleware\IdleConnection\Listener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\BacktraceDebugDataHolder';
$classes[] = 'Doctrine\ORM\Mapping\Driver\AttributeDriver';
$classes[] = 'Doctrine\ORM\Configuration';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\MappingDriver';
$classes[] = 'Doctrine\Persistence\Mapping\Driver\MappingDriverChain';
$classes[] = 'Doctrine\ORM\Mapping\UnderscoreNamingStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultQuoteStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultTypedFieldMapper';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Repository\ContainerRepositoryFactory';
$classes[] = 'Doctrine\ORM\Proxy\Autoloader';
$classes[] = 'Doctrine\ORM\EntityManager';
$classes[] = 'Symfony\Bridge\Doctrine\PropertyInfo\DoctrineExtractor';
$classes[] = 'Doctrine\ORM\Tools\AttachEntityListenersListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ManagerConfigurator';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\DoctrineDbalCacheAdapterSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\RememberMeTokenProviderDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\LockStoreSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\PdoSessionHandlerSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\MessengerTransportDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\Messenger\DoctrineClearEntityManagerWorkerSubscriber';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntityValidator';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UlidGenerator';
$classes[] = 'Symfony\Component\Uid\Factory\UlidFactory';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator';
$classes[] = 'Symfony\Component\Uid\Factory\UuidFactory';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\EventListener\SchemaFilterListener';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ErrorController';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\SerializerErrorRenderer';
$classes[] = 'Symfony\Bridge\Twig\ErrorRenderer\TwigErrorRenderer';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer';
$classes[] = 'Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ErrorListener';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\InlineFragmentRenderer';
$classes[] = 'Symfony\Component\HttpClient\HttpClient';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\HttpKernelRunner';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\ResponseRunner';
$classes[] = 'Symfony\Component\Runtime\SymfonyRuntime';
$classes[] = 'Symfony\Component\HttpKernel\HttpKernel';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableControllerResolver';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory';
$classes[] = 'App\Kernel';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleAwareListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\EnvelopeListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageLoggerListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessengerTransportListener';
$classes[] = 'Symfony\Component\Mailer\Transport\NativeTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\SendmailTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Transports';
$classes[] = 'Symfony\Component\Mailer\Transport';
$classes[] = 'Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\HandleMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\SendMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Transport\Sender\SendersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\TraceableMiddleware';
$classes[] = 'Symfony\Component\Messenger\TraceableMessageBus';
$classes[] = 'Symfony\Component\Messenger\MessageBus';
$classes[] = 'Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageToFailureTransportListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnRestartSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnCustomStopExceptionListener';
$classes[] = 'Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Retry\MultiplierRetryStrategy';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageForRetryListener';
$classes[] = 'Symfony\Component\Messenger\RoutableMessageBus';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportInterface';
$classes[] = 'Symfony\Component\Messenger\Bridge\Doctrine\Transport\DoctrineTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\InMemory\InMemoryTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\PhpSerializer';
$classes[] = 'Symfony\Component\Messenger\Transport\Sync\SyncTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportFactory';
$classes[] = 'Symfony\Component\Mime\MimeTypes';
$classes[] = 'Symfony\Bridge\Monolog\Handler\ConsoleHandler';
$classes[] = 'Monolog\Handler\StreamHandler';
$classes[] = 'Monolog\Processor\PsrLogMessageProcessor';
$classes[] = 'Monolog\Handler\NullHandler';
$classes[] = 'Nelmio\CorsBundle\EventListener\CacheableResponseVaryListener';
$classes[] = 'Nelmio\CorsBundle\EventListener\CorsListener';
$classes[] = 'Nelmio\CorsBundle\Options\Resolver';
$classes[] = 'Nelmio\CorsBundle\Options\ConfigProvider';
$classes[] = 'Symfony\Component\Notifier\EventListener\NotificationLoggerListener';
$classes[] = 'Symfony\Component\Notifier\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ProfilerListener';
$classes[] = 'Symfony\Component\PropertyAccess\PropertyAccessor';
$classes[] = 'Symfony\Component\PropertyInfo\PropertyInfoExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpStanExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\SerializerExtractor';
$classes[] = 'Symfony\Component\HttpFoundation\RequestStack';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ResponseListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\Router';
$classes[] = 'Symfony\Component\DependencyInjection\ParameterBag\ContainerBag';
$classes[] = 'Symfony\Component\Routing\Matcher\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Routing\RequestContext';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\RouterListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\DelegatingLoader';
$classes[] = 'Symfony\Component\Config\Loader\LoaderResolver';
$classes[] = 'Symfony\Component\Routing\Loader\XmlFileLoader';
$classes[] = 'Symfony\Component\HttpKernel\Config\FileLocator';
$classes[] = 'Symfony\Component\Routing\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\GlobFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\DirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\ContainerLoader';
$classes[] = 'ApiPlatform\Symfony\Routing\ApiLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\AttributeRouteControllerLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeDirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\Psr4DirectoryLoader';
$classes[] = 'Symfony\Component\DependencyInjection\StaticEnvVarLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault';
$classes[] = 'Symfony\Component\String\LazyString';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AccessListener';
$classes[] = 'Symfony\Component\Security\Http\AccessMap';
$classes[] = 'Symfony\Component\Security\Core\Authentication\AuthenticationTrustResolver';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AuthorizationChecker';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ChannelListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ContextListener';
$classes[] = 'Symfony\Component\Security\Csrf\SameOriginCsrfTokenManager';
$classes[] = 'Symfony\Component\Security\Csrf\CsrfTokenManager';
$classes[] = 'Symfony\Component\Security\Csrf\TokenGenerator\UriSafeTokenGenerator';
$classes[] = 'Symfony\Component\Security\Csrf\TokenStorage\SessionTokenStorage';
$classes[] = 'Symfony\Component\Security\Core\Authorization\ExpressionLanguage';
$classes[] = 'ApiPlatform\Symfony\Security\Core\Authorization\ExpressionLanguageProvider';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallMap';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallContext';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallConfig';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\LazyFirewallContext';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ExceptionListener';
$classes[] = 'Symfony\Component\Security\Http\HttpUtils';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CheckCredentialsListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfProtectionListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserProviderListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\PasswordMigratingListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\SessionStrategyListener';
$classes[] = 'Symfony\Component\Security\Http\Session\SessionAuthenticationStrategy';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserCheckerListener';
$classes[] = 'Symfony\Component\Security\Core\User\InMemoryUserChecker';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfTokenClearingLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\Logout\LogoutUrlGenerator';
$classes[] = 'Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactory';
$classes[] = 'Symfony\Component\Security\Core\Role\RoleHierarchy';
$classes[] = 'Symfony\Bundle\SecurityBundle\Routing\LogoutRouteLoader';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage';
$classes[] = 'Symfony\Component\Security\Core\User\InMemoryUserProvider';
$classes[] = 'Symfony\Component\Security\Core\Validator\Constraints\UserPasswordValidator';
$classes[] = 'Symfony\Component\Serializer\DataCollector\SerializerDataCollector';
$classes[] = 'Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter';
$classes[] = 'Symfony\Component\DependencyInjection\ContainerInterface';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter';
$classes[] = 'Symfony\Component\HttpFoundation\Session\SessionFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorageFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\MetadataBag';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\Handler\StrictSessionHandler';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\SessionListener';
$classes[] = 'Symfony\Component\String\Slugger\AsciiSlugger';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\ControllersMapGenerator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\AutoImportLocator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\StimulusLoaderJavaScriptCompiler';
$classes[] = 'Symfony\UX\StimulusBundle\Ux\UxPackageReader';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigRuntime';
$classes[] = 'Symfony\Component\Translation\Loader\CsvFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuDatFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IniFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\JsonFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\MoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\QtFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuResFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\XliffFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Translation\LocaleSwitcher';
$classes[] = 'Symfony\Component\Translation\DataCollectorTranslator';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Translation\Translator';
$classes[] = 'Symfony\Component\Translation\Formatter\MessageFormatter';
$classes[] = 'Symfony\Component\Translation\IdentityTranslator';
$classes[] = 'Symfony\UX\Turbo\Doctrine\BroadcastListener';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\TwigBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\ImuxBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\IdAccessor';
$classes[] = 'Symfony\UX\Turbo\Request\RequestListener';
$classes[] = 'Symfony\UX\Turbo\Twig\TurboRuntime';
$classes[] = 'Twig\Cache\FilesystemCache';
$classes[] = 'Twig\Extension\CoreExtension';
$classes[] = 'Twig\Extension\EscaperExtension';
$classes[] = 'Twig\Extension\OptimizerExtension';
$classes[] = 'Twig\Extension\StagingExtension';
$classes[] = 'Twig\ExtensionSet';
$classes[] = 'Twig\Template';
$classes[] = 'Twig\TemplateWrapper';
$classes[] = 'Twig\Environment';
$classes[] = 'Twig\Loader\FilesystemLoader';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ProfilerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\TranslationExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\AssetExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\RoutingExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\YamlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\StopwatchExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ExpressionExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpFoundationExtension';
$classes[] = 'Symfony\Component\HttpFoundation\UrlHelper';
$classes[] = 'Symfony\Bridge\Twig\Extension\WebLinkExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\FormExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\LogoutUrlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SecurityExtension';
$classes[] = 'Symfony\Component\Security\Http\Impersonate\ImpersonateUrlGenerator';
$classes[] = 'Symfony\Bridge\Twig\Extension\DumpExtension';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Twig\DoctrineExtension';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Twig\WebProfilerExtension';
$classes[] = 'Symfony\Component\VarDumper\Dumper\HtmlDumper';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Profiler\CodeExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigExtension';
$classes[] = 'Symfony\UX\Turbo\Twig\TwigExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\StimulusTwigExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Helper\StimulusHelper';
$classes[] = 'Symfony\Bridge\Twig\AppVariable';
$classes[] = 'Twig\RuntimeLoader\ContainerRuntimeLoader';
$classes[] = 'Twig\Extra\TwigExtraBundle\MissingExtensionSuggestor';
$classes[] = 'Symfony\Bundle\TwigBundle\DependencyInjection\Configurator\EnvironmentConfigurator';
$classes[] = 'Symfony\Bridge\Twig\Form\TwigRendererEngine';
$classes[] = 'Symfony\Component\Form\FormRenderer';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageListener';
$classes[] = 'Symfony\Bridge\Twig\Mime\BodyRenderer';
$classes[] = 'Twig\Profiler\Profile';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelRuntime';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\FragmentUriGenerator';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapRuntime';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapRenderer';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfRuntime';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerRuntime';
$classes[] = 'Symfony\Component\HttpFoundation\UriSigner';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ValidateRequestListener';
$classes[] = 'Symfony\Component\Validator\ValidatorBuilder';
$classes[] = 'Symfony\Component\Validator\Validation';
$classes[] = 'Symfony\Component\Validator\ContainerConstraintValidatorFactory';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineInitializer';
$classes[] = 'Symfony\Component\Validator\Mapping\Loader\PropertyInfoLoader';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineLoader';
$classes[] = 'Symfony\Component\Validator\Constraints\EmailValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Validator\Constraints\NoSuspiciousCharactersValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\NotCompromisedPasswordValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\WhenValidator';
$classes[] = 'Symfony\Component\VarDumper\Cloner\VarCloner';
$classes[] = 'Symfony\Component\VarDumper\Server\Connection';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\SourceContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\RequestContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\CliContextProvider';
$classes[] = 'Symfony\Component\WebLink\EventListener\AddLinkHeaderListener';
$classes[] = 'Symfony\Component\WebLink\HttpHeaderSerializer';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ExceptionPanelController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\RouterController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\ContentSecurityPolicyHandler';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\NonceGenerator';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\EventListener\WebDebugToolbarListener';

$preloaded = Preloader::preload($classes);

import { useState, useEffect } from 'react';
import axios from 'axios';

const TerminationGenerator = () => {
  const [employees, setEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    employeeId: '',
    terminationType: 'fristgerecht',
    terminationDate: new Date().toISOString().split('T')[0],
    signatory: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Deutsche Monatsnamen
  const deutscheMonate = {
    1: "Januar", 2: "Februar", 3: "März", 4: "April", 5: "Mai", 6: "Juni",
    7: "Juli", 8: "August", 9: "September", 10: "Oktober", 11: "November", 12: "Dezember"
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    try {
      const response = await axios.get('http://localhost:8000/api/employees');
      setEmployees(response.data);
    } catch (error) {
      console.error('Fehler beim Laden der Mitarbeiter:', error);
      setMessage('Fehler beim Laden der Mitarbeiterdaten');
    }
  };

  const filteredEmployees = employees.filter(emp =>
    emp.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee);
    setFormData(prev => ({
      ...prev,
      employeeId: employee.id
    }));
    setSearchTerm(employee.name);
  };

  const formatDateDeutsch = (dateString) => {
    if (!dateString || dateString === 'Nicht angegeben') {
      return 'Nicht angegeben';
    }

    // Prüfe, ob das Datum im deutschen Format (DD.MM.YYYY) vorliegt
    if (dateString.includes('.')) {
      const parts = dateString.split('.');
      if (parts.length === 3) {
        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]);
        const year = parseInt(parts[2]);

        if (!isNaN(day) && !isNaN(month) && !isNaN(year) && month >= 1 && month <= 12) {
          return `${day}. ${deutscheMonate[month]} ${year}`;
        }
      }
    }

    // Fallback: Versuche als ISO-Datum zu parsen
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return `${date.getDate()}. ${deutscheMonate[date.getMonth() + 1]} ${date.getFullYear()}`;
    }

    return 'Ungültiges Datum';
  };

  const calculateTerminationDate = () => {
    const issueDate = new Date(formData.terminationDate);
    const entryDate = selectedEmployee?.entryDate ? new Date(selectedEmployee.entryDate) : null;

    switch (formData.terminationType) {
      case 'fristlos':
        return issueDate;
      case 'probezeit':
        const probationEnd = new Date(issueDate);
        probationEnd.setDate(probationEnd.getDate() + 14);
        return probationEnd;
      case 'fristgerecht':
        const fourWeeksLater = new Date(issueDate);
        fourWeeksLater.setDate(fourWeeksLater.getDate() + 28);

        const nextMonth15th = new Date(issueDate);
        nextMonth15th.setMonth(nextMonth15th.getMonth() + 1);
        nextMonth15th.setDate(15);

        if (fourWeeksLater <= nextMonth15th) {
          return nextMonth15th;
        } else {
          const monthEnd = new Date(fourWeeksLater);
          monthEnd.setMonth(monthEnd.getMonth() + 1);
          monthEnd.setDate(0); // Letzter Tag des Monats
          return monthEnd;
        }
      default:
        return issueDate;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!selectedEmployee) {
      setMessage('Bitte wählen Sie einen Mitarbeiter aus.');
      return;
    }

    if (!formData.signatory.trim()) {
      setMessage('Bitte geben Sie den Unterzeichner an.');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const terminationDate = calculateTerminationDate();

      const requestData = {
        employeeId: selectedEmployee.id,
        employeeName: selectedEmployee.name,
        employeeAddress: selectedEmployee.address || '',
        employeeZipCity: selectedEmployee.zipCity || '',
        terminationType: formData.terminationType,
        issueDate: formData.terminationDate,
        terminationDate: terminationDate.toISOString().split('T')[0],
        signatory: formData.signatory,
        entryDate: selectedEmployee.entryDate || null
      };

      const response = await axios.post('http://localhost:8000/api/termination/generate', requestData, {
        responseType: 'blob'
      });

      // Download der generierten Datei
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Kuendigung_${selectedEmployee.name.replace(/\s+/g, '_')}.docx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setMessage('Kündigungsschreiben wurde erfolgreich generiert und heruntergeladen.');

      // Formular zurücksetzen
      setFormData({
        employeeId: '',
        terminationType: 'fristgerecht',
        terminationDate: new Date().toISOString().split('T')[0],
        signatory: ''
      });
      setSelectedEmployee(null);
      setSearchTerm('');

    } catch (error) {
      console.error('Fehler beim Generieren der Kündigung:', error);
      setMessage('Fehler beim Generieren des Kündigungsschreibens.');
    } finally {
      setLoading(false);
    }
  };

  const getTerminationPreview = () => {
    if (!selectedEmployee) return null;

    const terminationDate = calculateTerminationDate();
    const entryDate = selectedEmployee.entryDate ? new Date(selectedEmployee.entryDate) : null;
    const today = new Date();
    const isInProbation = entryDate && (today - entryDate) < (90 * 24 * 60 * 60 * 1000);

    return (
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">Kündigungsvorschau</h4>
        <p className="text-sm text-blue-700">
          <strong>Mitarbeiter:</strong> {selectedEmployee.name}<br/>
          <strong>Kündigungsart:</strong> {formData.terminationType}<br/>
          <strong>Ausstellungsdatum:</strong> {formatDateDeutsch(formData.terminationDate)}<br/>
          <strong>Kündigungsdatum:</strong> {formatDateDeutsch(terminationDate.toISOString().split('T')[0])}<br/>
          {isInProbation && (
            <span className="text-yellow-600">
              <strong>Hinweis:</strong> Mitarbeiter ist noch in der Probezeit (Eintritt: {formatDateDeutsch(selectedEmployee.entryDate)})
            </span>
          )}
        </p>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center mb-6">
        <div className="bg-rewe-red p-3 rounded-lg mr-4">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div>
          <h3 className="text-xl font-semibold text-gray-800">Kündigungsgenerator</h3>
          <p className="text-gray-600">Erstellen Sie automatisch Kündigungsschreiben</p>
        </div>
      </div>

      {message && (
        <div className={`mb-4 p-4 rounded-lg ${message.includes('Fehler') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Mitarbeiterauswahl */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mitarbeiter auswählen
          </label>
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setSelectedEmployee(null);
                setFormData(prev => ({ ...prev, employeeId: '' }));
              }}
              placeholder="Name des Mitarbeiters eingeben..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rewe-red focus:border-transparent"
            />

            {searchTerm && !selectedEmployee && filteredEmployees.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {filteredEmployees.slice(0, 10).map((employee) => (
                  <button
                    key={employee.id}
                    type="button"
                    onClick={() => {
                      console.log('Selected employee:', employee);
                      console.log('Entry date from employee:', employee.entryDate);
                      setSelectedEmployee(employee);
                      setSearchTerm(employee.name);
                      setFormData(prev => ({ ...prev, employeeId: employee.id }));
                    }}
                    className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                  >
                    <div className="font-medium">{employee.name}</div>
                    <div className="text-sm text-gray-500">
                      {employee.department} • Markt {employee.marketNumber}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {selectedEmployee && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Ausgewählter Mitarbeiter</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Name:</span> {selectedEmployee.name}
              </div>
              <div>
                <span className="font-medium">Abteilung:</span> {selectedEmployee.department || 'Nicht angegeben'}
              </div>
              <div>
                <span className="font-medium">Markt:</span> {selectedEmployee.marketNumber || 'Nicht angegeben'}
              </div>
              <div>
                <span className="font-medium">Eintrittsdatum:</span> {selectedEmployee.entryDate ? formatDateDeutsch(selectedEmployee.entryDate) : 'Nicht angegeben'}
              </div>
              <div className="col-span-2">
                <span className="font-medium">Adresse:</span> {selectedEmployee.address || 'Nicht angegeben'} {selectedEmployee.zipCity || ''}
              </div>
            </div>
          </div>
        )}

        {/* Kündigungsart */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Kündigungsart
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="terminationType"
                value="fristlos"
                checked={formData.terminationType === 'fristlos'}
                onChange={(e) => setFormData(prev => ({ ...prev, terminationType: e.target.value }))}
                className="mr-2 text-rewe-red focus:ring-rewe-red"
              />
              <span>Fristlose Kündigung</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="terminationType"
                value="probezeit"
                checked={formData.terminationType === 'probezeit'}
                onChange={(e) => setFormData(prev => ({ ...prev, terminationType: e.target.value }))}
                className="mr-2 text-rewe-red focus:ring-rewe-red"
              />
              <span>Probezeitkündigung (14 Tage)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="terminationType"
                value="fristgerecht"
                checked={formData.terminationType === 'fristgerecht'}
                onChange={(e) => setFormData(prev => ({ ...prev, terminationType: e.target.value }))}
                className="mr-2 text-rewe-red focus:ring-rewe-red"
              />
              <span>Fristgerechte Kündigung (4 Wochen)</span>
            </label>
          </div>
        </div>

        {/* Ausstellungsdatum */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ausstellungsdatum
          </label>
          <input
            type="date"
            value={formData.terminationDate}
            onChange={(e) => setFormData(prev => ({ ...prev, terminationDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rewe-red focus:border-transparent"
            required
          />
        </div>

        {/* Unterzeichner */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Unterzeichner
          </label>
          <input
            type="text"
            value={formData.signatory}
            onChange={(e) => setFormData(prev => ({ ...prev, signatory: e.target.value }))}
            placeholder="Name des Unterzeichners"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rewe-red focus:border-transparent"
            required
          />
        </div>

        {getTerminationPreview()}

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading || !selectedEmployee}
            className="px-6 py-2 bg-rewe-red text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-rewe-red focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Generiere...' : 'Kündigung generieren'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TerminationGenerator;

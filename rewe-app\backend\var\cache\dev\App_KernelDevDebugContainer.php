<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerOgi04LG\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerOgi04LG/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerOgi04LG.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerOgi04LG\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerOgi04LG\App_KernelDevDebugContainer([
    'container.build_hash' => 'Ogi04LG',
    'container.build_id' => '424793a4',
    'container.build_time' => 1748098935,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerOgi04LG');

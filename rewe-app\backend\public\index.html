<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REWE Personalmanagement - Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #E30613;
            margin: 0;
        }
        .section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #E30613;
            margin: 20px 0;
        }
        .section h3 {
            margin-top: 0;
            color: #E30613;
        }
        .action-button {
            display: inline-block;
            background-color: #E30613;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .action-button:hover {
            background-color: #c00;
        }
        .action-button.secondary {
            background-color: #6c757d;
        }
        .action-button.secondary:hover {
            background-color: #545b62;
        }
        .status {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .code {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🏪 REWE Personalmanagement</h1>
            <p>Backend-Verwaltung</p>
        </div>

        <div class="status">
            ✅ Backend läuft erfolgreich auf Port 8000
        </div>

        <div class="section">
            <h3>🚀 Schnellstart</h3>
            <p>Erste Schritte für die Nutzung der Anwendung:</p>
            <a href="/create-test-data.php" class="action-button">1. Testdaten erstellen</a>
            <a href="http://localhost:5173" class="action-button" target="_blank">2. Frontend öffnen</a>
        </div>

        <div class="section">
            <h3>🔐 Anmeldedaten</h3>
            <div class="code">Benutzername: admin</div>
            <div class="code">Passwort: admin123</div>
        </div>

        <div class="section">
            <h3>🔧 Verwaltung</h3>
            <a href="/login-info.php" class="action-button secondary">Login-Informationen</a>
            <a href="/test-phpword.php" class="action-button secondary">PhpWord testen</a>
            <a href="/create-test-data.php" class="action-button secondary">Testdaten erstellen</a>
        </div>

        <div class="section">
            <h3>📡 API-Endpunkte</h3>
            <div class="code">POST /api/login - Benutzeranmeldung</div>
            <div class="code">GET /api/employees - Mitarbeiterliste</div>
            <div class="code">GET /api/departments - Abteilungen</div>
            <div class="code">GET /api/market-numbers - Marktnummern</div>
            <div class="code">POST /api/termination/generate - Kündigungsgenerator</div>
        </div>

        <div class="warning">
            <strong>⚠️ Wichtiger Hinweis:</strong><br>
            Falls Sie keine Mitarbeiterdaten sehen, klicken Sie zuerst auf "Testdaten erstellen".
            Dies erstellt Beispieldaten für die Demonstration der Anwendung.
        </div>

        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>REWE Personalmanagement System v1.0</p>
            <p>Entwickelt für die Verwaltung von Mitarbeiterdaten und Dokumenten</p>
        </div>
    </div>

    <script>
        // Automatische Weiterleitung zur Testdaten-Erstellung bei Bedarf
        function checkAndCreateTestData() {
            fetch('/api/employees')
                .then(response => response.json())
                .then(data => {
                    if (!data || data.length === 0) {
                        if (confirm('Keine Mitarbeiterdaten gefunden. Möchten Sie Testdaten erstellen?')) {
                            window.location.href = '/create-test-data.php';
                        }
                    }
                })
                .catch(error => {
                    console.log('Fehler beim Prüfen der Mitarbeiterdaten:', error);
                });
        }

        // Prüfe beim Laden der Seite
        window.addEventListener('load', checkAndCreateTestData);
    </script>
</body>
</html>

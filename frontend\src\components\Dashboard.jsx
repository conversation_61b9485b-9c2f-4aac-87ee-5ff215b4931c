import { useState, useEffect } from 'react';
import axios from 'axios';
import EmployeeList from './EmployeeList';
import AdminDashboard from './AdminDashboard';

const Dashboard = ({ user, onLogout }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('employees');

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        // In a real app, this would fetch from the API
        setNotifications([
          { id: 1, message: 'Neue Mitarbeiterdaten wurden importiert', date: new Date() },
          { id: 2, message: 'Besprechung am Montag um 10:00 Uhr', date: new Date() },
          { id: 3, message: '<PERSON>rlaubsant<PERSON> von <PERSON>', date: new Date() }
        ]);
        setLoading(false);
      } catch (err) {
        console.error('<PERSON><PERSON> beim Laden der Benachrichtigungen:', err);
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const handleLogout = async () => {
    try {
      await axios.post('http://localhost:8000/api/logout');
      localStorage.removeItem('user');
      onLogout();
    } catch (err) {
      console.error('Fehler beim Abmelden:', err);
    }
  };

  const formatDate = (date) => {
    return date.toLocaleString('de-DE');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'employees':
        return <EmployeeList />;
      case 'skills':
        return <div className="p-4">Skilltrees-Funktion wird bald verfügbar sein.</div>;
      case 'todos':
        return <div className="p-4">To-Do-Funktion wird bald verfügbar sein.</div>;
      case 'calendar':
        return <div className="p-4">Kalender-Funktion wird bald verfügbar sein.</div>;
      case 'admin':
        return user.roles.includes('ROLE_ADMIN') ? (
          <AdminDashboard />
        ) : (
          <div className="p-4 text-red-600">Zugriff verweigert. Sie benötigen Admin-Rechte.</div>
        );
      default:
        return <div className="p-4">Wählen Sie eine Option aus dem Menü.</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-rewe-red text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <img
              src="https://www.rewe-dugandzic.de/wp-content/uploads/2023/03/rewe-logo.png"
              alt="REWE Logo"
              className="h-10 mr-4"
            />
            <h1 className="text-2xl font-bold">REWE Personalmanagement</h1>
          </div>
          <div className="flex items-center">
            <span className="mr-4">Willkommen, {user.username}</span>
            <button
              onClick={handleLogout}
              className="bg-white text-rewe-red px-4 py-2 rounded hover:bg-gray-100"
            >
              Abmelden
            </button>
          </div>
        </div>
      </header>

      <div className="container mx-auto mt-6 flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white rounded-lg shadow-md p-4 mr-6">
          <nav>
            <ul>
              <li className="mb-2">
                <button
                  onClick={() => setActiveTab('employees')}
                  className={`w-full text-left px-4 py-2 rounded ${activeTab === 'employees' ? 'bg-rewe-red text-white' : 'hover:bg-gray-100'}`}
                >
                  Mitarbeiter
                </button>
              </li>
              <li className="mb-2">
                <button
                  onClick={() => setActiveTab('skills')}
                  className={`w-full text-left px-4 py-2 rounded ${activeTab === 'skills' ? 'bg-rewe-red text-white' : 'hover:bg-gray-100'}`}
                >
                  Skilltrees
                </button>
              </li>
              <li className="mb-2">
                <button
                  onClick={() => setActiveTab('todos')}
                  className={`w-full text-left px-4 py-2 rounded ${activeTab === 'todos' ? 'bg-rewe-red text-white' : 'hover:bg-gray-100'}`}
                >
                  To-Dos
                </button>
              </li>
              <li className="mb-2">
                <button
                  onClick={() => setActiveTab('calendar')}
                  className={`w-full text-left px-4 py-2 rounded ${activeTab === 'calendar' ? 'bg-rewe-red text-white' : 'hover:bg-gray-100'}`}
                >
                  Kalender
                </button>
              </li>
              {user.roles.includes('ROLE_ADMIN') && (
                <li className="mb-2">
                  <button
                    onClick={() => setActiveTab('admin')}
                    className={`w-full text-left px-4 py-2 rounded ${activeTab === 'admin' ? 'bg-rewe-red text-white' : 'hover:bg-gray-100'}`}
                  >
                    Admin-Bereich
                  </button>
                </li>
              )}
            </ul>
          </nav>

          <div className="mt-8">
            <h3 className="font-bold text-lg mb-2">Benachrichtigungen</h3>
            {loading ? (
              <p>Lade Benachrichtigungen...</p>
            ) : (
              <ul className="space-y-2">
                {notifications.map(notification => (
                  <li key={notification.id} className="bg-gray-50 p-2 rounded">
                    <p className="text-sm">{notification.message}</p>
                    <p className="text-xs text-gray-500">{formatDate(notification.date)}</p>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 bg-white rounded-lg shadow-md overflow-hidden">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;

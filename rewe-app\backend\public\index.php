<?php

// Einfache Routing-Logik für die API
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// CORS Headers
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($requestMethod === 'OPTIONS') {
    exit(0);
}

// Login-Endpunkt
if (strpos($requestUri, '/api/login') !== false && $requestMethod === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $username = $data['username'] ?? '';
    $password = $data['password'] ?? '';

    error_log("Login attempt: Username: $username");

    if ($username === 'admin' && $password === 'admin123') {
        error_log("Login successful");
        header('Content-Type: application/json');
        echo json_encode([
            'username' => 'admin',
            'roles' => ['ROLE_ADMIN'],
            'token' => 'demo-token'
        ]);
    } else {
        error_log("Login failed");
        header('Content-Type: application/json');
        http_response_code(401);
        echo json_encode(['message' => 'Ungültige Anmeldedaten']);
    }
    exit;
}

// Route zu spezifischen Dateien
if (strpos($requestUri, '/api/employees') !== false) {
    require_once 'db.php';
    exit;
} elseif (strpos($requestUri, '/api/termination') !== false) {
    require_once 'termination.php';
    exit;
} elseif (strpos($requestUri, '/test-phpword') !== false) {
    require_once 'test-phpword.php';
    exit;
} elseif (strpos($requestUri, '/login-info') !== false) {
    require_once 'login-info.php';
    exit;
}

// Einfache 404-Antwort für unbekannte Routen
header('Content-Type: application/json');
http_response_code(404);
echo json_encode(['message' => 'Endpunkt nicht gefunden']);

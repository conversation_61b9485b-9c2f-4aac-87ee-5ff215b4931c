<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REWE Personalmanagement - Login Info</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #E30613;
            margin: 0;
        }
        .credentials {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #E30613;
            margin: 20px 0;
        }
        .credentials h3 {
            margin-top: 0;
            color: #E30613;
        }
        .credential-item {
            margin: 10px 0;
            font-family: monospace;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
        }
        .status {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            background-color: #E30613;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .links a:hover {
            background-color: #c00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🏪 REWE Personalmanagement</h1>
            <p>Backend-Informationen</p>
        </div>

        <div class="status">
            ✅ Backend läuft erfolgreich auf Port 8000
        </div>

        <div class="credentials">
            <h3>🔐 Anmeldedaten</h3>
            <div class="credential-item">
                <strong>Benutzername:</strong> admin
            </div>
            <div class="credential-item">
                <strong>Passwort:</strong> admin123
            </div>
        </div>

        <div class="credentials">
            <h3>🔗 Verfügbare Endpunkte</h3>
            <div class="credential-item">
                <strong>Login:</strong> POST /api/login
            </div>
            <div class="credential-item">
                <strong>Mitarbeiter:</strong> GET /api/employees
            </div>
            <div class="credential-item">
                <strong>Kündigungsgenerator:</strong> POST /api/termination/generate
            </div>
            <div class="credential-item">
                <strong>PhpWord Test:</strong> GET /test-phpword.php
            </div>
        </div>

        <div class="links">
            <a href="http://localhost:5173" target="_blank">🖥️ Frontend öffnen</a>
            <a href="/test-phpword.php" target="_blank">🧪 PhpWord testen</a>
        </div>

        <div style="margin-top: 30px; font-size: 0.9em; color: #666;">
            <p><strong>Hinweis:</strong> Diese Seite zeigt die aktuellen Anmeldedaten für das REWE Personalmanagement System.</p>
            <p>Stellen Sie sicher, dass sowohl Backend (Port 8000) als auch Frontend (Port 5173) laufen.</p>
        </div>
    </div>
</body>
</html>

import { useState } from 'react';
import TerminationGenerator from './TerminationGenerator';

const AdminDashboard = () => {
  const [activeAdminTab, setActiveAdminTab] = useState('overview');

  const renderAdminContent = () => {
    switch (activeAdminTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Admin-Übersicht</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800">Systemstatus</h4>
                  <p className="text-blue-600">Alle Systeme funktionsfähig</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800">Letzte Importe</h4>
                  <p className="text-green-600">Heute: 2 Importe</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800">Wartung</h4>
                  <p className="text-yellow-600">Nächste Wartung: 15.01.2024</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Manuelle Änderungen</h3>
              <p className="text-gray-600">Hier werden alle manuellen Änderungen angezeigt.</p>
            </div>
          </div>
        );
      case 'termination':
        return <TerminationGenerator />;
      case 'imports':
        return (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Import-Verwaltung</h3>
            <p className="text-gray-600">Import-Funktionen werden hier angezeigt.</p>
          </div>
        );
      case 'users':
        return (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Benutzerverwaltung</h3>
            <p className="text-gray-600">Benutzerverwaltung wird hier angezeigt.</p>
          </div>
        );
      default:
        return <div>Wählen Sie eine Option aus dem Admin-Menü.</div>;
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Admin-Bereich</h2>
      
      {/* Admin Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveAdminTab('overview')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeAdminTab === 'overview'
                ? 'bg-white text-rewe-red shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            Übersicht
          </button>
          <button
            onClick={() => setActiveAdminTab('termination')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeAdminTab === 'termination'
                ? 'bg-white text-rewe-red shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            Kündigungsgenerator
          </button>
          <button
            onClick={() => setActiveAdminTab('imports')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeAdminTab === 'imports'
                ? 'bg-white text-rewe-red shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            Importe
          </button>
          <button
            onClick={() => setActiveAdminTab('users')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeAdminTab === 'users'
                ? 'bg-white text-rewe-red shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            Benutzer
          </button>
        </nav>
      </div>

      {/* Admin Content */}
      <div className="min-h-96">
        {renderAdminContent()}
      </div>
    </div>
  );
};

export default AdminDashboard;

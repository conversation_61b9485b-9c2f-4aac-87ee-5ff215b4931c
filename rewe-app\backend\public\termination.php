<?php
// Aktiviere Error Reporting für Debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '../var/log/termination_errors.log');

header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Content-Type: application/json');
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Debug-Funktion
function debugLog($message) {
    error_log('[TERMINATION DEBUG] ' . $message);
}

debugLog('Termination script started');

try {
    // Lade Composer Autoloader
    $autoloadPath = '../vendor/autoload.php';
    if (!file_exists($autoloadPath)) {
        throw new Exception('Composer autoloader nicht gefunden: ' . $autoloadPath);
    }
    require_once $autoloadPath;
    debugLog('Autoloader loaded');

    // Lese JSON-Daten
    $input = file_get_contents('php://input');
    debugLog('Raw input: ' . $input);

    $data = json_decode($input, true);
    debugLog('Decoded data: ' . print_r($data, true));

    if (!$data) {
        throw new Exception('Ungültige JSON-Daten: ' . json_last_error_msg());
    }

    // Validierung
    $requiredFields = ['employeeName', 'terminationType', 'signatory'];
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        throw new Exception('Fehlende Pflichtfelder: ' . implode(', ', $missingFields));
    }

    // Prüfe PhpWord
    if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
        throw new Exception('PhpWord ist nicht verfügbar');
    }
    debugLog('PhpWord class available');

    // Validiere terminationDate
    if (!isset($data['terminationDate']) || empty($data['terminationDate'])) {
        throw new Exception('Kündigungsdatum fehlt');
    }

    // Erstelle Word-Dokument
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    debugLog('PhpWord instance created');

    // Füge eine Sektion hinzu
    $section = $phpWord->addSection();
    debugLog('Section added');

    // Einfacher Inhalt
    $section->addText('REWE Markt GmbH', ['bold' => true, 'size' => 14]);
    $section->addTextBreak(2);

    $section->addText('Kündigung', ['bold' => true, 'size' => 12]);
    $section->addTextBreak(2);

    $section->addText('Sehr geehrte/r ' . $data['employeeName'] . ',');
    $section->addTextBreak();

    try {
        $terminationDate = new DateTime($data['terminationDate']);
        debugLog('Termination date parsed: ' . $terminationDate->format('Y-m-d'));
    } catch (Exception $dateException) {
        throw new Exception('Ungültiges Kündigungsdatum: ' . $data['terminationDate'] . ' - ' . $dateException->getMessage());
    }

    $terminationText = '';

    switch ($data['terminationType']) {
        case 'fristlos':
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis fristlos zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
        case 'probezeit':
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis während der Probezeit zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
        case 'fristgerecht':
        default:
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis ordentlich zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
    }

    $section->addText($terminationText);
    $section->addTextBreak(2);

    $section->addText('Mit freundlichen Grüßen');
    $section->addTextBreak(2);
    $section->addText($data['signatory']);
    $section->addText('Marktleitung');

    debugLog('Document content added');

    // Speichere temporär
    $tempDir = sys_get_temp_dir();
    debugLog('Temp directory: ' . $tempDir);

    if (!is_writable($tempDir)) {
        throw new Exception('Temp-Verzeichnis ist nicht beschreibbar: ' . $tempDir);
    }

    $tempFile = tempnam($tempDir, 'termination_') . '.docx';
    debugLog('Temp file: ' . $tempFile);

    try {
        $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        debugLog('Writer created');

        $writer->save($tempFile);
        debugLog('File saved to: ' . $tempFile);

        if (!file_exists($tempFile)) {
            throw new Exception('Temporäre Datei wurde nicht erstellt');
        }

        $fileSize = filesize($tempFile);
        debugLog('File size: ' . $fileSize . ' bytes');

        if ($fileSize === 0) {
            throw new Exception('Erstellte Datei ist leer');
        }

    } catch (Exception $writeException) {
        throw new Exception('Fehler beim Speichern der Datei: ' . $writeException->getMessage());
    }

    // Sende Datei
    $filename = 'Kuendigung_' . str_replace(' ', '_', $data['employeeName']) . '.docx';

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    debugLog('Headers sent, reading file');

    readfile($tempFile);
    unlink($tempFile);

    debugLog('File sent and cleaned up');

} catch (Exception $e) {
    debugLog('ERROR: ' . $e->getMessage());
    debugLog('Stack trace: ' . $e->getTraceAsString());

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>

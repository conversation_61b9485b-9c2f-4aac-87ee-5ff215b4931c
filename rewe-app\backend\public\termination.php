<?php
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Lade Composer Autoloader
require_once '../vendor/autoload.php';

try {
    // Lese JSON-Daten
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Ungültige JSON-Daten');
    }
    
    // Validierung
    if (!isset($data['employeeName']) || !isset($data['terminationType']) || !isset($data['signatory'])) {
        throw new Exception('Fehlende Pflichtfelder');
    }
    
    // Prüfe PhpWord
    if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
        throw new Exception('PhpWord ist nicht verfügbar');
    }
    
    // Erstelle Word-Dokument
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    
    // Füge eine Sektion hinzu
    $section = $phpWord->addSection();
    
    // Einfacher Inhalt
    $section->addText('REWE Markt GmbH', ['bold' => true, 'size' => 14]);
    $section->addTextBreak(2);
    
    $section->addText('Kündigung', ['bold' => true, 'size' => 12]);
    $section->addTextBreak(2);
    
    $section->addText('Sehr geehrte/r ' . $data['employeeName'] . ',');
    $section->addTextBreak();
    
    $terminationDate = new DateTime($data['terminationDate']);
    $terminationText = '';
    
    switch ($data['terminationType']) {
        case 'fristlos':
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis fristlos zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
        case 'probezeit':
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis während der Probezeit zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
        case 'fristgerecht':
        default:
            $terminationText = 'hiermit kündigen wir Ihr Arbeitsverhältnis ordentlich zum ' . $terminationDate->format('d.m.Y') . '.';
            break;
    }
    
    $section->addText($terminationText);
    $section->addTextBreak(2);
    
    $section->addText('Mit freundlichen Grüßen');
    $section->addTextBreak(2);
    $section->addText($data['signatory']);
    $section->addText('Marktleitung');
    
    // Speichere temporär
    $tempFile = tempnam(sys_get_temp_dir(), 'termination_') . '.docx';
    $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $writer->save($tempFile);
    
    // Sende Datei
    $filename = 'Kuendigung_' . str_replace(' ', '_', $data['employeeName']) . '.docx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($tempFile));
    
    readfile($tempFile);
    unlink($tempFile);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>

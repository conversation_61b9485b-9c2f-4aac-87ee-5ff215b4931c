[2025-05-25 08:51:48] Termination endpoint called
[2025-05-25 08:51:48] Raw input: {"employeeId":147,"employeeName":"<PERSON><PERSON><PERSON>, <PERSON>","employeeAddress":"<PERSON><PERSON><PERSON>.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristlos","issueDate":"2025-05-26","terminationDate":"2025-05-26","signatory":"<PERSON><PERSON> Ha<PERSON>","entryDate":null}
[2025-05-25 08:51:48] Parsed data: {"employeeId":147,"employeeName":"<PERSON><PERSON><PERSON>, <PERSON>","employeeAddress":"<PERSON><PERSON><PERSON>r.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristlos","issueDate":"2025-05-26","terminationDate":"2025-05-26","signatory":"<PERSON><PERSON> Ha<PERSON>","entryDate":null}
[2025-05-25 08:51:48] Hole Daten für Mitarbeiter ID 147, Personalnummer 1812297
[2025-05-25 08:51:48] Employee data: {"id":147,"personnelNumber":"1812297","name":"<PERSON><PERSON><PERSON>, <PERSON>","department":"10","location":"","marketNumber":"56869475512","workArea":"","birthdate":"25.04.2006","position":"Verkaufshilfe","zipCity":"50181 Bedburg","address":"Friedrich-<PERSON>-<PERSON>r.\n86","mobile":"015112382117","status":"inaktiv"}
[2025-05-25 08:51:48] Eintrittsdatum für Kündigung: nicht verfügbar
[2025-05-25 08:51:48] Probezeit möglich: nein
[2025-05-25 08:51:48] PhpWord class available
[2025-05-25 08:51:48] PhpWord instance created
[2025-05-25 08:51:49] Section added
[2025-05-25 08:55:03] Termination endpoint called
[2025-05-25 08:55:03] Raw input: {"employeeId":147,"employeeName":"Mecit, Ali","employeeAddress":"Friedrich-Ebert-Str.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristlos","issueDate":"2025-05-25","terminationDate":"2025-05-25","signatory":"Jochen Harff","entryDate":null}
[2025-05-25 08:55:03] Parsed data: {"employeeId":147,"employeeName":"Mecit, Ali","employeeAddress":"Friedrich-Ebert-Str.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristlos","issueDate":"2025-05-25","terminationDate":"2025-05-25","signatory":"Jochen Harff","entryDate":null}
[2025-05-25 08:55:03] Hole Daten für Mitarbeiter ID 147, Personalnummer 1812297
[2025-05-25 08:55:03] Anzahl Gehaltsdaten in DB: 296
[2025-05-25 08:55:03] Gefundene Gehaltsdaten für 1812297: {"Monat_Jahr":"04.2025","PersNR":"1812297","Mitarbeiter":"Ali Mecit","Stelle":"Verkaufshilfe","Kostenstelle":"6869475512","Abteilung":"10","Geburtsdatum":"25.04.2006","Eintrittsdatum\n(anrechenbar)":"15.05.2024","Austrittsdatum":"","Vertragsart":"2. Befristung","Tarifgebiet K\u00fcrzel":"P1","Tarifgebiet":"NRW\/W\/Partner","Tarifgruppe":"GFB-G-M","Tarifstufe":"#","BS-Kennzeichen":"GFB","Wochen-\nStunden":"9","Soll-\nStunden":"39","Stunden\nbezahlt":"39","Mehr-\narbeitsstd. bez.":"","Brutto\nStd-Lohn":"12.82","vertragliche\nBez\u00fcge":"501.27","Basisbez\u00fcge\nvertraglich":"501.27","Zulagen \nGesamt\nvertraglich":"","Funktions-\nzulagen\nvertraglich":"","freiwillige\nZulagen\nvertraglich":"","Mehrarbeits-\npauschale\nvertraglich":"","Zulage\nKasse\nvertraglich":"","sonstige\nZulagen\nvertraglich":"","bezahlte\nFehlzeiten":"","AG-SV-\nGesamt":"142.21","Gesamt-\nkosten\/ h":"16.49948717948718","Gesamt-\nkosten":"643.48","Entgeltabr.\nDB1010":"","Pr\u00e4mien\nDB1010":"","Urlaubs\/\nWeihnGeld":"","Entgeltabr.\nDB1020":"","Entgeltabr.\nDB1040":"","Entgeltabr.\nDB1050":"","Entgeltabr.\nDB1000":"643.48","lfd. Bez\u00fcge":"501.27","dav. \nBasisentgelt":"501.27","Mehrarb.\nbez.":"","Sp\u00e4t-\nzuschl\u00e4ge":"","Nacht-\nzuschl\u00e4ge":"","Einmal-\nbez\u00fcge":"","Lohn-\nausgleich":"","Zusch\u00fcsse":"","Vertretung":"","sonstige\nBez\u00fcge":"0"}
[2025-05-25 08:55:03] Anzahl gefundene Gehaltsdaten für 1812297: 1
[2025-05-25 08:55:03] Neueste Gehaltsdaten: {"Monat_Jahr":"04.2025","PersNR":"1812297","Mitarbeiter":"Ali Mecit","Stelle":"Verkaufshilfe","Kostenstelle":"6869475512","Abteilung":"10","Geburtsdatum":"25.04.2006","Eintrittsdatum\n(anrechenbar)":"15.05.2024","Austrittsdatum":"","Vertragsart":"2. Befristung","Tarifgebiet K\u00fcrzel":"P1","Tarifgebiet":"NRW\/W\/Partner","Tarifgruppe":"GFB-G-M","Tarifstufe":"#","BS-Kennzeichen":"GFB","Wochen-\nStunden":"9","Soll-\nStunden":"39","Stunden\nbezahlt":"39","Mehr-\narbeitsstd. bez.":"","Brutto\nStd-Lohn":"12.82","vertragliche\nBez\u00fcge":"501.27","Basisbez\u00fcge\nvertraglich":"501.27","Zulagen \nGesamt\nvertraglich":"","Funktions-\nzulagen\nvertraglich":"","freiwillige\nZulagen\nvertraglich":"","Mehrarbeits-\npauschale\nvertraglich":"","Zulage\nKasse\nvertraglich":"","sonstige\nZulagen\nvertraglich":"","bezahlte\nFehlzeiten":"","AG-SV-\nGesamt":"142.21","Gesamt-\nkosten\/ h":"16.49948717948718","Gesamt-\nkosten":"643.48","Entgeltabr.\nDB1010":"","Pr\u00e4mien\nDB1010":"","Urlaubs\/\nWeihnGeld":"","Entgeltabr.\nDB1020":"","Entgeltabr.\nDB1040":"","Entgeltabr.\nDB1050":"","Entgeltabr.\nDB1000":"643.48","lfd. Bez\u00fcge":"501.27","dav. \nBasisentgelt":"501.27","Mehrarb.\nbez.":"","Sp\u00e4t-\nzuschl\u00e4ge":"","Nacht-\nzuschl\u00e4ge":"","Einmal-\nbez\u00fcge":"","Lohn-\nausgleich":"","Zusch\u00fcsse":"","Vertretung":"","sonstige\nBez\u00fcge":"0"}
[2025-05-25 08:55:03] Kein Eintrittsdatum in Gehaltsdaten gefunden. Verfügbare Felder: Monat_Jahr, PersNR, Mitarbeiter, Stelle, Kostenstelle, Abteilung, Geburtsdatum, Eintrittsdatum
(anrechenbar), Austrittsdatum, Vertragsart, Tarifgebiet Kürzel, Tarifgebiet, Tarifgruppe, Tarifstufe, BS-Kennzeichen, Wochen-
Stunden, Soll-
Stunden, Stunden
bezahlt, Mehr-
arbeitsstd. bez., Brutto
Std-Lohn, vertragliche
Bezüge, Basisbezüge
vertraglich, Zulagen 
Gesamt
vertraglich, Funktions-
zulagen
vertraglich, freiwillige
Zulagen
vertraglich, Mehrarbeits-
pauschale
vertraglich, Zulage
Kasse
vertraglich, sonstige
Zulagen
vertraglich, bezahlte
Fehlzeiten, AG-SV-
Gesamt, Gesamt-
kosten/ h, Gesamt-
kosten, Entgeltabr.
DB1010, Prämien
DB1010, Urlaubs/
WeihnGeld, Entgeltabr.
DB1020, Entgeltabr.
DB1040, Entgeltabr.
DB1050, Entgeltabr.
DB1000, lfd. Bezüge, dav. 
Basisentgelt, Mehrarb.
bez., Spät-
zuschläge, Nacht-
zuschläge, Einmal-
bezüge, Lohn-
ausgleich, Zuschüsse, Vertretung, sonstige
Bezüge
[2025-05-25 08:55:03] Employee data: {"id":147,"personnelNumber":"1812297","name":"Mecit, Ali","department":"10","location":"","marketNumber":"56869475512","workArea":"","birthdate":"25.04.2006","position":"Verkaufshilfe","zipCity":"50181 Bedburg","address":"Friedrich-Ebert-Str.\n86","mobile":"015112382117","status":"inaktiv"}
[2025-05-25 08:55:03] Eintrittsdatum für Kündigung: nicht verfügbar
[2025-05-25 08:55:03] Probezeit möglich: nein
[2025-05-25 08:55:03] PhpWord class available
[2025-05-25 08:55:03] PhpWord instance created
[2025-05-25 08:55:03] Section added
[2025-05-25 09:00:52] Termination endpoint called
[2025-05-25 09:00:52] Raw input: {"employeeId":147,"employeeName":"Mecit, Ali","employeeAddress":"Friedrich-Ebert-Str.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristgerecht","issueDate":"2025-05-25","terminationDate":"2025-06-30","signatory":"asd","entryDate":"15.05.2024"}
[2025-05-25 09:00:52] Parsed data: {"employeeId":147,"employeeName":"Mecit, Ali","employeeAddress":"Friedrich-Ebert-Str.\n86","employeeZipCity":"50181 Bedburg","terminationType":"fristgerecht","issueDate":"2025-05-25","terminationDate":"2025-06-30","signatory":"asd","entryDate":"15.05.2024"}
[2025-05-25 09:00:52] Hole Daten für Mitarbeiter ID 147, Personalnummer 1812297
[2025-05-25 09:00:52] Anzahl Gehaltsdaten in DB: 296
[2025-05-25 09:00:52] Gefundene Gehaltsdaten für 1812297: {"Monat_Jahr":"04.2025","PersNR":"1812297","Mitarbeiter":"Ali Mecit","Stelle":"Verkaufshilfe","Kostenstelle":"6869475512","Abteilung":"10","Geburtsdatum":"25.04.2006","Eintrittsdatum\n(anrechenbar)":"15.05.2024","Austrittsdatum":"","Vertragsart":"2. Befristung","Tarifgebiet K\u00fcrzel":"P1","Tarifgebiet":"NRW\/W\/Partner","Tarifgruppe":"GFB-G-M","Tarifstufe":"#","BS-Kennzeichen":"GFB","Wochen-\nStunden":"9","Soll-\nStunden":"39","Stunden\nbezahlt":"39","Mehr-\narbeitsstd. bez.":"","Brutto\nStd-Lohn":"12.82","vertragliche\nBez\u00fcge":"501.27","Basisbez\u00fcge\nvertraglich":"501.27","Zulagen \nGesamt\nvertraglich":"","Funktions-\nzulagen\nvertraglich":"","freiwillige\nZulagen\nvertraglich":"","Mehrarbeits-\npauschale\nvertraglich":"","Zulage\nKasse\nvertraglich":"","sonstige\nZulagen\nvertraglich":"","bezahlte\nFehlzeiten":"","AG-SV-\nGesamt":"142.21","Gesamt-\nkosten\/ h":"16.49948717948718","Gesamt-\nkosten":"643.48","Entgeltabr.\nDB1010":"","Pr\u00e4mien\nDB1010":"","Urlaubs\/\nWeihnGeld":"","Entgeltabr.\nDB1020":"","Entgeltabr.\nDB1040":"","Entgeltabr.\nDB1050":"","Entgeltabr.\nDB1000":"643.48","lfd. Bez\u00fcge":"501.27","dav. \nBasisentgelt":"501.27","Mehrarb.\nbez.":"","Sp\u00e4t-\nzuschl\u00e4ge":"","Nacht-\nzuschl\u00e4ge":"","Einmal-\nbez\u00fcge":"","Lohn-\nausgleich":"","Zusch\u00fcsse":"","Vertretung":"","sonstige\nBez\u00fcge":"0"}
[2025-05-25 09:00:52] Anzahl gefundene Gehaltsdaten für 1812297: 1
[2025-05-25 09:00:52] Neueste Gehaltsdaten: {"Monat_Jahr":"04.2025","PersNR":"1812297","Mitarbeiter":"Ali Mecit","Stelle":"Verkaufshilfe","Kostenstelle":"6869475512","Abteilung":"10","Geburtsdatum":"25.04.2006","Eintrittsdatum\n(anrechenbar)":"15.05.2024","Austrittsdatum":"","Vertragsart":"2. Befristung","Tarifgebiet K\u00fcrzel":"P1","Tarifgebiet":"NRW\/W\/Partner","Tarifgruppe":"GFB-G-M","Tarifstufe":"#","BS-Kennzeichen":"GFB","Wochen-\nStunden":"9","Soll-\nStunden":"39","Stunden\nbezahlt":"39","Mehr-\narbeitsstd. bez.":"","Brutto\nStd-Lohn":"12.82","vertragliche\nBez\u00fcge":"501.27","Basisbez\u00fcge\nvertraglich":"501.27","Zulagen \nGesamt\nvertraglich":"","Funktions-\nzulagen\nvertraglich":"","freiwillige\nZulagen\nvertraglich":"","Mehrarbeits-\npauschale\nvertraglich":"","Zulage\nKasse\nvertraglich":"","sonstige\nZulagen\nvertraglich":"","bezahlte\nFehlzeiten":"","AG-SV-\nGesamt":"142.21","Gesamt-\nkosten\/ h":"16.49948717948718","Gesamt-\nkosten":"643.48","Entgeltabr.\nDB1010":"","Pr\u00e4mien\nDB1010":"","Urlaubs\/\nWeihnGeld":"","Entgeltabr.\nDB1020":"","Entgeltabr.\nDB1040":"","Entgeltabr.\nDB1050":"","Entgeltabr.\nDB1000":"643.48","lfd. Bez\u00fcge":"501.27","dav. \nBasisentgelt":"501.27","Mehrarb.\nbez.":"","Sp\u00e4t-\nzuschl\u00e4ge":"","Nacht-\nzuschl\u00e4ge":"","Einmal-\nbez\u00fcge":"","Lohn-\nausgleich":"","Zusch\u00fcsse":"","Vertretung":"","sonstige\nBez\u00fcge":"0"}
[2025-05-25 09:00:52] Verwende Eintrittsdatum aus neuestem Datensatz (Feld: Eintrittsdatum
(anrechenbar)): 15.05.2024
[2025-05-25 09:00:52] Employee data: {"id":147,"personnelNumber":"1812297","name":"Mecit, Ali","department":"10","location":"","marketNumber":"56869475512","workArea":"","birthdate":"25.04.2006","position":"Verkaufshilfe","zipCity":"50181 Bedburg","address":"Friedrich-Ebert-Str.\n86","mobile":"015112382117","status":"inaktiv","entryDate":"15.05.2024"}
[2025-05-25 09:00:52] Eintrittsdatum für Kündigung: 15.05.2024
[2025-05-25 09:00:52] Probezeit möglich: nein
[2025-05-25 09:00:52] PhpWord class available
[2025-05-25 09:00:52] PhpWord instance created
[2025-05-25 09:00:52] Section added

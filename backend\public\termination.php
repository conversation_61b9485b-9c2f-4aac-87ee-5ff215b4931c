<?php
require_once __DIR__ . '/../vendor/autoload.php';

header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Debug-Logging-Funktion
function debugLog($message) {
    $logFile = __DIR__ . '/../var/log/termination_errors.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    error_log($message);
}

debugLog('Termination endpoint called');

// Prüfe Request-Methode
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debugLog('Invalid request method: ' . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['error' => 'Nur POST-Requests erlaubt']);
    exit;
}

// Lade Eingabedaten
$input = file_get_contents('php://input');
debugLog('Raw input: ' . $input);

$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    debugLog('JSON decode error: ' . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['error' => 'Ungültige JSON-Daten']);
    exit;
}

debugLog('Parsed data: ' . json_encode($data));

// Deutsche Monatsnamen
$deutscheMonate = [
    1 => "Januar", 2 => "Februar", 3 => "März", 4 => "April", 5 => "Mai", 6 => "Juni",
    7 => "Juli", 8 => "August", 9 => "September", 10 => "Oktober", 11 => "November", 12 => "Dezember"
];

function formatDateDeutsch($dateString) {
    global $deutscheMonate;
    $date = new DateTime($dateString);
    return $date->format('j') . '. ' . $deutscheMonate[(int)$date->format('n')] . ' ' . $date->format('Y');
}

function calculateTerminationDate($terminationType, $issueDate, $entryDate = null) {
    $issueDateTime = new DateTime($issueDate);

    switch ($terminationType) {
        case 'fristlos':
            // Fristlose Kündigung: sofort
            return $issueDateTime;

        case 'probezeit':
            // Probezeitkündigung: 14 Tage
            return $issueDateTime->add(new DateInterval('P14D'));

        case 'fristgerecht':
        default:
            // Fristgerechte Kündigung: 4 Wochen, bevorzugt zum 15. des Folgemonats oder Monatsende
            $vierWochenSpaeter = clone $issueDateTime;
            $vierWochenSpaeter->add(new DateInterval('P28D'));

            $folgemonat15 = clone $issueDateTime;
            $folgemonat15->add(new DateInterval('P1M'));
            $folgemonat15->setDate($folgemonat15->format('Y'), $folgemonat15->format('n'), 15);

            if ($vierWochenSpaeter <= $folgemonat15) {
                return $folgemonat15;
            } else {
                // Monatsende nach 4 Wochen
                $monatsende = clone $vierWochenSpaeter;
                $monatsende->add(new DateInterval('P1M'));
                $monatsende->setDate($monatsende->format('Y'), $monatsende->format('n'), 1);
                $monatsende->sub(new DateInterval('P1D'));
                return $monatsende;
            }
    }
}

// Funktion zum Abrufen der Mitarbeiterdaten mit korrekter Eintrittsdatum-Logik
function getEmployeeDataForTermination($employeeId) {
    require_once 'db.php';
    global $db;

    // Finde den Mitarbeiter
    $employee = null;
    foreach ($db['employees'] as $emp) {
        if ($emp['id'] == $employeeId) {
            $employee = $emp;
            break;
        }
    }

    if (!$employee) {
        return null;
    }

    $personnelNumber = $employee['personnelNumber'];
    debugLog("Hole Daten für Mitarbeiter ID $employeeId, Personalnummer $personnelNumber");

    // Sammle alle Gehaltsdaten für diesen Mitarbeiter (gleiche Logik wie in getEmployeeDetails)
    $salaryData = [];
    foreach ($db['salary_data'] as $key => $data) {
        if ((strpos($key, $personnelNumber . '_') === 0) ||
            (isset($data['PersNr']) && $data['PersNr'] == $personnelNumber)) {
            $salaryData[] = $data;
        }
    }

    // Sortiere nach Monat_Jahr (neueste zuerst)
    if (!empty($salaryData)) {
        usort($salaryData, function($a, $b) {
            if (!isset($a['Monat_Jahr']) || !isset($b['Monat_Jahr'])) {
                return 0;
            }
            $monthYearA = explode('.', $a['Monat_Jahr']);
            $monthYearB = explode('.', $b['Monat_Jahr']);

            if (count($monthYearA) == 2 && count($monthYearB) == 2) {
                $yearA = intval($monthYearA[1]);
                $monthA = intval($monthYearA[0]);
                $yearB = intval($monthYearB[1]);
                $monthB = intval($monthYearB[0]);

                if ($yearA != $yearB) {
                    return $yearB - $yearA; // Neuestes Jahr zuerst
                }
                return $monthB - $monthA; // Neuester Monat zuerst
            }
            return strcmp($b['Monat_Jahr'], $a['Monat_Jahr']);
        });

        $latestSalaryData = $salaryData[0];

        // Verwende Eintrittsdatum aus den neuesten Gehaltsdaten (gleiche Logik wie in db.php)
        if (isset($latestSalaryData['Eintrittsdatum(anrechenbar)']) && !empty($latestSalaryData['Eintrittsdatum(anrechenbar)'])) {
            $employee['entryDate'] = $latestSalaryData['Eintrittsdatum(anrechenbar)'];
            debugLog("Verwende Eintrittsdatum aus neuestem Datensatz: " . $employee['entryDate']);
        }
    }

    return $employee;
}

try {
    // Validiere Eingabedaten
    $requiredFields = ['employeeId', 'terminationType', 'issueDate', 'signatory'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        throw new Exception('Fehlende Pflichtfelder: ' . implode(', ', $missingFields));
    }

    // Hole Mitarbeiterdaten mit korrekter Eintrittsdatum-Logik
    $employee = getEmployeeDataForTermination($data['employeeId']);

    if (!$employee) {
        throw new Exception('Mitarbeiter nicht gefunden');
    }

    debugLog('Employee data: ' . json_encode($employee));

    // Extrahiere weitere Daten
    $terminationType = $data['terminationType'];
    $issueDate = $data['issueDate'];
    $signatory = $data['signatory'];
    $entryDate = $employee['entryDate'] ?? null;

    debugLog("Eintrittsdatum für Kündigung: " . ($entryDate ?? 'nicht verfügbar'));

    // Bestimme Anrede (einfache Logik basierend auf Namen)
    $employeeName = $employee['name'];
    $anrede = (strpos(strtolower($employeeName), 'frau') !== false ||
               preg_match('/\b(anna|maria|petra|sabine|andrea|julia|sandra|nicole|stefanie|melanie)\b/i', $employeeName))
               ? 'Frau' : 'Herr';

    // Bestimme Ort basierend auf Marktnummer
    $ort = 'Bedburg'; // Standard
    $marketNumber = $employee['marketNumber'] ?? '';
    if (strpos($marketNumber, '5671') !== false) $ort = 'Grevenbroich';
    elseif (strpos($marketNumber, '5877') !== false) $ort = 'Köln';
    elseif (strpos($marketNumber, '5512') !== false) $ort = 'Bedburg';
    elseif (strpos($marketNumber, '5461') !== false) $ort = 'Swisttal';

    // Berechne Kündigungsdatum
    $terminationDate = calculateTerminationDate($terminationType, $issueDate, $entryDate);

    // Prüfe Probezeit (weniger als 3 Monate)
    $probezeitMoeglich = false;
    if ($entryDate) {
        $entryDateTime = new DateTime($entryDate);
        $issueDateTime = new DateTime($issueDate);
        $diff = $issueDateTime->diff($entryDateTime);
        $probezeitMoeglich = ($diff->days < 90);
    }

    debugLog("Probezeit möglich: " . ($probezeitMoeglich ? 'ja' : 'nein'));

    // Prüfe PhpWord
    if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
        throw new Exception('PhpWord ist nicht verfügbar');
    }

    debugLog('PhpWord class available');

    // Erstelle Word-Dokument
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    debugLog('PhpWord instance created');

    // Füge eine Sektion hinzu
    $section = $phpWord->addSection([
        'marginTop' => \PhpOffice\PhpWord\Shared\Converter::cmToTwip(2.5),
        'marginBottom' => \PhpOffice\PhpWord\Shared\Converter::cmToTwip(2),
        'marginLeft' => \PhpOffice\PhpWord\Shared\Converter::cmToTwip(2.5),
        'marginRight' => \PhpOffice\PhpWord\Shared\Converter::cmToTwip(2)
    ]);

    debugLog('Section added');

    // Schriftarten definieren
    $fontCalibri11 = ['name' => 'Calibri', 'size' => 11];
    $fontCalibri8 = ['name' => 'Calibri', 'size' => 8];
    $fontCalibri11Bold = ['name' => 'Calibri', 'size' => 11, 'bold' => true];

    // Kopfzeile mit Logo (rechtsbündig)
    $header = $section->getHeader();
    $headerParagraph = $header->addTextRun(['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);

    // Versuche Logo hinzuzufügen (falls vorhanden)
    $logoPath = __DIR__ . '/../../LOGO-REWE-DUGA-1-300x293.png';
    if (file_exists($logoPath)) {
        $headerParagraph->addImage($logoPath, [
            'width' => \PhpOffice\PhpWord\Shared\Converter::inchToCm(1.87),
            'height' => \PhpOffice\PhpWord\Shared\Converter::inchToCm(1.83),
            'wrappingStyle' => 'inline'
        ]);
    }

    // 1,5 Leerzeilen vor dem Formular
    $section->addTextBreak(1);
    $section->addText('', $fontCalibri11, ['spaceAfter' => 90]); // Halbe Zeile

    // Briefkopf und Adressinformationen (linksbündig)
    $section->addText('REWE Dugandzic GmbH & Co. oHG', $fontCalibri8, ['spaceAfter' => 0]);
    $section->addText('Sankt-Rochus-Straße 30, 50181 Bedburg', $fontCalibri8, ['spaceAfter' => 180]);

    // Übergabeart
    $ubergabeart = ($terminationType === 'fristlos') ? '- per Einschreiben / Einwurf -' : '- persönliche Übergabe / Posteinwurf -';
    $section->addText($ubergabeart, $fontCalibri11Bold, ['spaceAfter' => 0]);

    // Empfängeradresse
    $section->addText($anrede, $fontCalibri11, ['spaceAfter' => 0]);
    $section->addText($employeeName, $fontCalibri11, ['spaceAfter' => 0]);

    // Adresse aus Mitarbeiterdaten
    if (!empty($employee['address'])) {
        $section->addText($employee['address'], $fontCalibri11, ['spaceAfter' => 0]);
    }
    if (!empty($employee['zipCity'])) {
        $section->addText($employee['zipCity'], $fontCalibri11, ['spaceAfter' => 180]);
    } else {
        $section->addTextBreak(1);
    }

    // Ort und Datum (rechtsbündig)
    $section->addText($ort . ', ' . formatDateDeutsch($issueDate), $fontCalibri11Bold, [
        'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END,
        'spaceAfter' => 180
    ]);

    // Überschrift
    $section->addText('Kündigung Ihres Arbeitsverhältnisses', $fontCalibri11Bold, ['spaceAfter' => 180]);

    // Anrede
    $section->addText("Guten Tag $anrede $employeeName,", $fontCalibri11, ['spaceAfter' => 180]);

    // Kündigungstext basierend auf Kündigungsart
    $terminationText = '';
    if ($terminationType === 'fristlos') {
        $vorsorgedatum = clone $terminationDate;
        $vorsorgedatum->add(new DateInterval('P1M'));
        $vorsorgedatum->setDate($vorsorgedatum->format('Y'), $vorsorgedatum->format('n'), 15);

        $terminationText = 'ich kündige das mit Ihnen bestehende Arbeitsverhältnis fristlos mit Zugang dieses Schreibens, rein vorsorglich form- und fristgerecht zum ' . formatDateDeutsch($vorsorgedatum->format('Y-m-d')) . ', hilfsweise zum nächstzulässigen Termin.';
    } elseif ($terminationType === 'probezeit') {
        $terminationText = 'ich kündige das mit Ihnen bestehende Arbeitsverhältnis mit Zugang dieses Schreibens, form- und fristgerecht in der Probezeit zum ' . formatDateDeutsch($terminationDate->format('Y-m-d')) . ', hilfsweise zum nächstzulässigen Termin.';
    } else {
        $terminationText = 'ich kündige das mit Ihnen bestehende Arbeitsverhältnis mit Zugang dieses Schreibens, form- und fristgerecht zum ' . formatDateDeutsch($terminationDate->format('Y-m-d')) . ', hilfsweise zum nächstzulässigen Termin.';
    }

    $section->addText($terminationText, $fontCalibri11, ['spaceAfter' => 180]);

    // Standardtext mit unterstrichenen Wörtern
    $paragraph = $section->addTextRun(['spaceAfter' => 180]);
    $paragraph->addText('Ich weise Sie darauf hin, dass Sie ausnahmslos dazu verpflichtet sind, sich ', $fontCalibri11);
    $paragraph->addText('unverzüglich', array_merge($fontCalibri11, ['underline' => \PhpOffice\PhpWord\Style\Font::UNDERLINE_SINGLE]));
    $paragraph->addText(' nach Kenntnis des Beendigungszeitpunktes ', $fontCalibri11);
    $paragraph->addText('persönlich', array_merge($fontCalibri11, ['underline' => \PhpOffice\PhpWord\Style\Font::UNDERLINE_SINGLE]));
    $paragraph->addText(' bei der zuständigen Agentur für Arbeit arbeitsuchend zu melden. Andernfalls mindert sich Ihr Anspruch auf Arbeitslosengeld. Sie müssen sich durch persönliche Vorsprache mit der Agentur für Arbeit in Verbindung setzen und sich dort ergänzende Informationen einholen.', $fontCalibri11);

    $section->addText('Weiterhin sind Sie verpflichtet, aktiv nach einer Beschäftigung zu suchen.', $fontCalibri11, ['spaceAfter' => 180]);

    $section->addText('Ihre Arbeitspapiere und ein Zeugnis über den Beschäftigungszeitraum erhalten Sie nach Beendigung des Arbeitsverhältnisses für Ihre Unterlagen zugeschickt.', $fontCalibri11, ['spaceAfter' => 180]);

    // Absender
    $section->addText('REWE Dugandzic GmbH & Co. oHG', $fontCalibri11, ['spaceAfter' => 180]);

    // Neuer Absatz vor Unterzeichner
    $section->addTextBreak(1);

    // Unterzeichner
    $section->addText($signatory, $fontCalibri11, ['spaceAfter' => 180]);

    debugLog('Document content added');

    // Speichere temporär
    $tempDir = sys_get_temp_dir();
    debugLog('Temp directory: ' . $tempDir);

    if (!is_writable($tempDir)) {
        throw new Exception('Temp-Verzeichnis ist nicht beschreibbar: ' . $tempDir);
    }

    $tempFile = tempnam($tempDir, 'termination_') . '.docx';
    debugLog('Temp file: ' . $tempFile);

    try {
        $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        debugLog('Writer created');

        $writer->save($tempFile);
        debugLog('File saved to: ' . $tempFile);

        if (!file_exists($tempFile)) {
            throw new Exception('Temporäre Datei wurde nicht erstellt');
        }

        $fileSize = filesize($tempFile);
        debugLog('File size: ' . $fileSize . ' bytes');

        if ($fileSize === 0) {
            throw new Exception('Erstellte Datei ist leer');
        }

    } catch (Exception $writeException) {
        throw new Exception('Fehler beim Speichern der Datei: ' . $writeException->getMessage());
    }

    // Sende Datei
    $filename = 'Kuendigung_' . str_replace([' ', ','], '_', $employeeName) . '.docx';

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    debugLog('Headers sent, reading file');

    readfile($tempFile);
    unlink($tempFile);

    debugLog('File sent and cleaned up');

} catch (Exception $e) {
    debugLog('ERROR: ' . $e->getMessage());
    debugLog('Stack trace: ' . $e->getTraceAsString());

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>

<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once 'db.php';

// CORS Headers
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Debug-Funktion
function debugLog($message) {
    $logFile = __DIR__ . '/../var/log/termination_errors.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Einfache Word-Dokument-Erstellung
function createSimpleWordDocument($data) {
    debugLog('Starting simple Word document creation');

    try {
        // Erstelle PhpWord-Instanz mit korrekten Einstellungen
        $phpWord = new \PhpOffice\PhpWord\PhpWord();

        // Setze Dokumenteigenschaften
        $properties = $phpWord->getDocInfo();
        $properties->setCreator('REWE Dugandzic GmbH & Co. oHG');
        $properties->setTitle('Kündigungsschreiben');

        debugLog('PhpWord instance created');

        // Füge Sektion mit korrekten Einstellungen hinzu
        $section = $phpWord->addSection([
            'marginTop' => 1134,    // 2cm in Twips
            'marginBottom' => 1134,
            'marginLeft' => 1134,
            'marginRight' => 1134
        ]);
        debugLog('Section added');

        // Einfache, kompatible Schriftarten
        $font = ['name' => 'Times New Roman', 'size' => 11];
        $fontBold = ['name' => 'Times New Roman', 'size' => 11, 'bold' => true];

        // Briefkopf
        $section->addText('REWE Dugandzic GmbH & Co. oHG', $fontBold);
        $section->addText('Sankt-Rochus-Straße 30, 50181 Bedburg', $font);
        $section->addTextBreak(2);

        // Empfänger
        $section->addText($data['employeeName'], $font);
        if (!empty($data['employeeAddress'])) {
            $section->addText($data['employeeAddress'], $font);
        }
        if (!empty($data['employeeZipCity'])) {
            $section->addText($data['employeeZipCity'], $font);
        }
        $section->addTextBreak(2);

        // Datum
        $section->addText('Bedburg, ' . date('d.m.Y', strtotime($data['issueDate'])), $font);
        $section->addTextBreak(2);

        // Betreff
        $section->addText('Kündigung Ihres Arbeitsverhältnisses', $fontBold);
        $section->addTextBreak(1);

        // Anrede
        $section->addText('Sehr geehrte/r ' . $data['employeeName'] . ',', $font);
        $section->addTextBreak(1);

        // Kündigungstext
        $terminationText = 'hiermit kündigen wir das mit Ihnen bestehende Arbeitsverhältnis ';
        if ($data['terminationType'] === 'fristlos') {
            $terminationText .= 'fristlos zum ' . date('d.m.Y', strtotime($data['terminationDate'])) . '.';
        } else {
            $terminationText .= 'ordentlich zum ' . date('d.m.Y', strtotime($data['terminationDate'])) . '.';
        }

        $section->addText($terminationText, $font);
        $section->addTextBreak(2);

        // Standardtext
        $section->addText('Bitte melden Sie sich unverzüglich bei der Agentur für Arbeit arbeitsuchend.', $font);
        $section->addTextBreak(1);
        $section->addText('Ihre Arbeitspapiere erhalten Sie nach Beendigung des Arbeitsverhältnisses.', $font);
        $section->addTextBreak(3);

        // Grußformel
        $section->addText('Mit freundlichen Grüßen', $font);
        $section->addTextBreak(2);
        $section->addText('REWE Dugandzic GmbH & Co. oHG', $font);
        $section->addTextBreak(1);
        $section->addText($data['signatory'], $font);

        debugLog('Document content added successfully');
        return $phpWord;

    } catch (Exception $e) {
        debugLog('Error in createSimpleWordDocument: ' . $e->getMessage());
        throw $e;
    }
}

try {
    debugLog('Termination endpoint called');

    // Lese JSON-Daten
    $input = file_get_contents('php://input');
    debugLog('Raw input: ' . $input);

    $data = json_decode($input, true);
    if (!$data) {
        throw new Exception('Ungültige JSON-Daten');
    }

    debugLog('Parsed data: ' . json_encode($data));

    // Validiere erforderliche Felder
    $required = ['employeeId', 'employeeName', 'terminationType', 'issueDate', 'terminationDate', 'signatory'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("Feld '$field' ist erforderlich");
        }
    }

    // Prüfe PhpWord
    if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
        throw new Exception('PhpWord ist nicht verfügbar');
    }

    debugLog('PhpWord class available');

    // Erstelle Word-Dokument
    $phpWord = createSimpleWordDocument($data);
    debugLog('Word document created');

    // Speichere temporär
    $tempDir = sys_get_temp_dir();
    $tempFile = tempnam($tempDir, 'termination_') . '.docx';
    debugLog('Temp file: ' . $tempFile);

    try {
        $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        debugLog('Writer created');

        // Setze Writer-Optionen für bessere Kompatibilität
        if (method_exists($writer, 'setUseDiskCaching')) {
            $writer->setUseDiskCaching(true);
        }

        $writer->save($tempFile);
        debugLog('File saved to: ' . $tempFile);

        // Zusätzliche Validierung
        if (!file_exists($tempFile)) {
            throw new Exception('Datei wurde nicht erstellt');
        }

        $fileSize = filesize($tempFile);
        if ($fileSize < 1000) { // Word-Dateien sollten mindestens 1KB haben
            throw new Exception('Datei ist zu klein (möglicherweise korrupt): ' . $fileSize . ' bytes');
        }

        debugLog('File validation successful: ' . $fileSize . ' bytes');

    } catch (Exception $writerException) {
        debugLog('Writer error: ' . $writerException->getMessage());
        throw new Exception('Fehler beim Erstellen der Word-Datei: ' . $writerException->getMessage());
    }

    $fileSize = filesize($tempFile);

    // Sende Datei
    $filename = 'Kuendigung_' . str_replace([' ', ','], '_', $data['employeeName']) . '.docx';

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    debugLog('Headers sent, reading file');

    readfile($tempFile);
    unlink($tempFile);

    debugLog('File sent and cleaned up');

} catch (Exception $e) {
    debugLog('ERROR: ' . $e->getMessage());
    debugLog('Stack trace: ' . $e->getTraceAsString());

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'php_version' => phpversion(),
            'phpword_available' => class_exists('PhpOffice\PhpWord\PhpWord'),
            'temp_dir' => sys_get_temp_dir(),
            'temp_writable' => is_writable(sys_get_temp_dir())
        ]
    ]);
}
?>
